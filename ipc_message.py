IPC_REQUEST_HEADER_LENGTH = 13
IPC_RESPONSE_HEADER_LENGTH = 6

class IpcRequest:
    def __init__(self):
        self.type = 0x00
        self.current_cycle = 0x0000000000000000 # 8 bytes
        self.length = 0x00000000
        self.data = None

    def header_to_bytes(self):
        headers = self.type.to_bytes(1, "big")
        headers += self.current_cycle.to_bytes(8, "big")
        headers += self.length.to_bytes(4, "big")
        return headers

    def header_from_bytes(self, headers: bytes):
        type_bytes = headers[0:1]
        self.type = int.from_bytes(type_bytes, 'big', signed=False)

        cycle_bytes = headers[1:9]
        self.current_cycle = int.from_bytes(cycle_bytes, 'big', signed=False)

        length_bytes = headers[9:IPC_REQUEST_HEADER_LENGTH]
        self.length = int.from_bytes(length_bytes, 'big', signed=False)

class IpcResponse:
    def __init__(self):
        self.status = 0x0000
        self.length = 0x00000000
        self.data = None

    def header_to_bytes(self):
        headers = self.status.to_bytes(2, "big")
        headers += self.length.to_bytes(4, "big")
        return headers

    def header_from_bytes(self, headers: bytes):
        status_bytes = headers[0:2]
        self.status = int.from_bytes(status_bytes, 'big', signed=False)

        length_bytes = headers[2:IPC_RESPONSE_HEADER_LENGTH]
        self.length = int.from_bytes(length_bytes, 'big', signed=False)