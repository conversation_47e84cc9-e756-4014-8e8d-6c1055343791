Current Version: v0.4

- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - 

v0.4: released @June 6, 2025
本版本新增内容：Simulator 延迟信息反标
1. 所有操作均引入 latency
2. 将浮点数搬运至 CIM 时，默认 TMU 进行 Align 操作，且需要进行两次数据读取
本版本修订内容：
1. NoC 指令运行时 Router 未正确设置为 Busy，现已修复

- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - 

v0.3: released @April 29, 2025
本版本修订内容：
1. Runningprim 到 Finishedprim，考虑到可能有多个指令在同一周期跑完，因此会先将他们的位置全部记录下来 (index_list)，然后再 pop(index) 出队列；这样是有 bug 的，因为每次 pop 之后，指令的 index 就变了；多个指令同一周期跑完概率较小，因此该 bug 之前未被检测到，在 gqa_decode_flashattention_0429 将出现此情况，现已修复正确的逻辑
2. 引入 NOC 指令后，一个 Core 可以通过 NOC 修改另一个 Core 资源的状态，使得在特殊下情况下可能引发资源状态错误，现在修改 NOC 修改资源的逻辑为：开始时，Core A 同时将自己和 Core B 的资源置为 Busy；结束时，Core A 和 Core B 各自仅释放自己的资源。原逻辑为：开始和结束时，Core A 同时将自己和 Core B 的资源置为 Busy/IDLE

- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - 

v0.2: released @April 27, 2025
本版本修订内容：
1. 接收数据的 Core 未显式地表明其在执行 NOC 指令，这使得与 NOC 指令有数据依赖的指令会在接收数据之前就执行，造成错误，因此需要修改为接收数据的 Core 也在显式地执行 NOC 指令

- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - 

v0.1: released @April 17, 2025
本版本修订内容：
1. Modules.py 中 SRAM 内部验证地址时：if byte_base 需改为 if byte_base is not None，否则对于 byte_base 恰好为 0 的 case 将出现 bug
2. CIM bit-serial 输入指令集上的行为已经修改成 cycle_cost 等于输入数据位宽，因此需要 去掉对于 page_read 的计算，去掉对于 mantissa_width + 1b_hidden_one + 1b_sign 的转换，直接使用数据位宽
3. 可视化输出结果，对齐至 LSU, MPU, VPU, TMU, NOC
4. DRAM 配置改为 256bit 位宽，新增 DRAM_Frequency=400M，因此其等效至 800M 就是 128bit
5. NOC 指令 DRAM 直接到 DRAM 的传输也改为合法的

- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - 

v0.0: released @April 12, 2025
The very first released version of the PNMOp Simulator!