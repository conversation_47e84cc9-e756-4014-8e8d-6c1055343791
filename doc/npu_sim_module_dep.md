```mermaid
graph TD
    %% Main Entry Points
    Demo[npu_demo.py] --> SIMTop[SIMTop.py]
    Demo --> IPC[IPC Modules]
    Demo --> Decoder[decoder.py]
    
    %% Core Simulation Components
    SIMTop --> NPUCore[NPUCore.py]
    SIMTop --> NoC[NoC.py]
    SIMTop --> SIMTemplate[SIMTemplate.py]
    SIMTop --> GlobalSettings[GlobalSettings.py]
    SIMTop --> GoldenModel[GoldenModel.py]
    SIMTop --> Backend[sim_backend_torch.py]
    SIMTop --> Visualize[Visualize.py]
    SIMTop --> Utility[Utility.py]
    
    %% NPU Core Dependencies
    NPUCore --> Modules[Modules.py]
    NPUCore --> SIMTemplate
    NPUCore --> GlobalSettings
    NPUCore --> Backend
    
    %% Hardware Modules
    Modules --> SIMTemplate
    Modules --> GlobalSettings
    
    %% NoC Dependencies
    NoC --> GlobalSettings
    
    %% Template Dependencies
    SIMTemplate --> GlobalSettings
    SIMTemplate --> Modules
    SIMTemplate --> Utility
    SIMTemplate --> Backend
    
    %% IPC and Communication
    IPC --> Message[ipc_message.py]
    IPC --> Server[ipc_socket_server.py]
    IPC --> Client[ipc_socket_client.py]
    
    %% Decoder Dependencies
    Decoder --> RegGroup[reggroup.py]
    Decoder --> Constants[npu_constants.py]
    Decoder --> TensorInfo[tensorinfo.py]
    
    %% External Dependencies
    Backend --> PyTorch[PyTorch Library]
    Visualize --> Matplotlib[Matplotlib Library]
    Utility --> NumPy[NumPy Library]
    
    %% Data Flow
    SIMInput[SIMInput.py] --> SIMTemplate
    
    %% Configuration
    GlobalSettings --> |Configures| NPUCore
    GlobalSettings --> |Configures| Modules
    GlobalSettings --> |Configures| NoC
    
    %% Styling
    classDef entryPoint fill:#e1f5fe,stroke:#01579b
    classDef coreModule fill:#fff3e0,stroke:#e65100
    classDef hwModule fill:#f3e5f5,stroke:#4a148c
    classDef external fill:#e8f5e8,stroke:#1b5e20
    classDef config fill:#fce4ec,stroke:#880e4f
    
    class Demo,SIMTop entryPoint
    class NPUCore,NoC,SIMTemplate coreModule
    class Modules,Backend hwModule
    class PyTorch,Matplotlib,NumPy external
    class GlobalSettings config
```