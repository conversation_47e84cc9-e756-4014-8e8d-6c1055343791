## 6.2 NoC Primitive

NoC Primitive为负责向NPU Core中的数据分发及回传NoC指令，其通过NoC网络传输至目的NPU Core。NoC传输分为两种情况，一种为RISC-V只需要负责将1对NPU Core和目的NPU Core之间的数据传输过程，另一种情况中，为了充分利用NoC网络的带宽，RISC-V需要同时驱动多对NPU Core和目的NPU Core之间的数据传输。在该情况下，RISC-V需要同时给绝对的NPU Core发送指令。基于以上两种情况，设定NICE指令和VNICE两类扩展指令集。

### 1. NICE

| Inst Name | Inst Type | Field | | | | |
| :--- | :--- | :--- | :--- | :--- | :--- | :--- |
| | | **funct7** | **xd, xs1, xs2** | **rdval** | **rs1val** | **rs2val** |
| noc_src_drv | NICE | 001_0000 | 0, 1, 1 | / | base_addr | dest_idx |
| noc_dest_drv | NICE | 001_0001 | 0, 1, 1 | / | base_addr | src_idx |
| noc_fence_drv | NICE | 001_0010 | 1, 0, 0 | 1 | / | / |

* `noc_src`指令发送至源NPU Core端的扩展指令，`base_addr`表示源NPU Core中所需要传输数据的local memory基地址，`dest_idx`表示目的NPU Core的索引。
* `noc_dest`指令发送至目的NPU Core端的扩展指令，`base_addr`表示目的NPU Core中所需要存放数据的local memory（local memory or global memory）基地址，`src_idx`表明该数据的源NPU Core所在位置。
* `noc_fence`发送至被Mask的NPU核，表示源NPU和目的NPU中所有NoC相关指令执行完毕时，返回至RISC-V中。

### 2. VNICE

* `noc_src`指令发送至源NPU Core端的扩展指令，`base_addr`向量中的每个元素表示对应源NPU Core中所需要传输数据的local memory基地址，`dest_idx`向量中的每个元素表示对应目的NPU Core的索引。
* `noc_dest`指令发送至目的NPU Core端的扩展指令，`base_addr`向量中的每个元素表示目的NPU Core中所需要保存装数据的local memory基地址，`src_idx`向量中的每个元素表明该数据的对应的源NPU Core所在索引。

| Inst Name | Inst Type | | Field | | | | |
| :--- | :--- | :--- | :--- | :--- | :--- | :--- | :--- |
| | | | **funct7** | **xd, xs1, xs2** | **rdval** | **rs1val** | **rs2val** |
| noc_src_drv | VNICE | x,x | 0_0110 | 0, 1, 1 | / | base_addr | dest_idx |
| noc_dest_drv | VNICE | x,x | 0_0111 | 0, 1, 1 | / | base_addr | src_idx |

NPU Core索引分布如下图所示，为了不让硬件的NoC拓扑对上层软件的影响，因此采用下列的Core编号方式：

idx由chip_id, group_id, core_id共同组成，其中：
* `idx[31:0] = {chip_id_y[31:24], chip_id_x[23:16], group_id[15:8], core_id[7:0]}`

---

在其中的一个chip内部，以2x4的NoC网络为例，每个group内的core_id编号如下（一种颜色代表一个group）：

**坐标**
| | Y=0 | Y=1 | Y=2 | Y=3 | | Y=0 | Y=1 | Y=2 | Y=3 |
| :--- | :---: | :---: | :---: | :---: | :--- | :---: | :---: | :---: | :---: |
| **X=0** | 0 | 1 | 2 | 3 | | 0 | 1 | 2 | 3 |
| **X=0** | 0 | 1 | 2 | 3 | | 0 | 1 | 2 | 3 |

以4x4的NoC网络为例，每个group内的core_id编号如下（一种颜色代表一个group）：

**坐标**
| | Y=0 | Y=1 | | Y=0 | Y=1 |
| :--- | :---: | :---: | :--- | :---: | :---: |
| **X=0** | 0 | 1 | | 0 | 1 |
| **X=2** | 2 | 3 | | 2 | 3 |
| **X=0** | 0 | 1 | | 0 | 1 |
| **X=2** | 2 | 3 | | 2 | 3 |

### 3. 编程说明

对于NoC操作需要分成以下几步：

a. 设定NPU Group和NPU Mask寄存器，将源NPU Core和目的NPU Core同时选中，发送noc_fence指令，确保所有的NPU Core在该条fence指令之前的noc操作都已执行完毕
b. 设定NPU Group和NPU Mask寄存器，将源NPU Core和目的NPU Core同时选中，并发送相应的数据配置信息（**6.1.2.8 NoC Configuration Register**）相关指令
c. 设定NPU Group和NPU Mask寄存器，将目的NPU Core选中，并发送相应的noc_dest指令，NPU Core接收至指令后，确保noc_dest所对应资源处于空闲状态，才能让NPU Core处于准备好接收数据状态。
d. 设定NPU Group和NPU Mask寄存器，将源NPU Core选中，并发送相应的noc_src指令，NPU Core将数据发送至NoC端口中。
e. 源NPU和目的NPU之间建立数据传输链路