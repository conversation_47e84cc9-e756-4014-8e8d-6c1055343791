<svg aria-roledescription="class" role="graphics-document document" viewBox="0 0 1948.464111328125 1970" style="max-width: 1948.464111328125px;" class="classDiagram" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns="http://www.w3.org/2000/svg" width="100%" id="mermaid-0de37219-61f3-43fe-b3cf-29fed43ccd82"><style>#mermaid-0de37219-61f3-43fe-b3cf-29fed43ccd82{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;fill:#ccc;}#mermaid-0de37219-61f3-43fe-b3cf-29fed43ccd82 .error-icon{fill:#a44141;}#mermaid-0de37219-61f3-43fe-b3cf-29fed43ccd82 .error-text{fill:#ddd;stroke:#ddd;}#mermaid-0de37219-61f3-43fe-b3cf-29fed43ccd82 .edge-thickness-normal{stroke-width:1px;}#mermaid-0de37219-61f3-43fe-b3cf-29fed43ccd82 .edge-thickness-thick{stroke-width:3.5px;}#mermaid-0de37219-61f3-43fe-b3cf-29fed43ccd82 .edge-pattern-solid{stroke-dasharray:0;}#mermaid-0de37219-61f3-43fe-b3cf-29fed43ccd82 .edge-thickness-invisible{stroke-width:0;fill:none;}#mermaid-0de37219-61f3-43fe-b3cf-29fed43ccd82 .edge-pattern-dashed{stroke-dasharray:3;}#mermaid-0de37219-61f3-43fe-b3cf-29fed43ccd82 .edge-pattern-dotted{stroke-dasharray:2;}#mermaid-0de37219-61f3-43fe-b3cf-29fed43ccd82 .marker{fill:lightgrey;stroke:lightgrey;}#mermaid-0de37219-61f3-43fe-b3cf-29fed43ccd82 .marker.cross{stroke:lightgrey;}#mermaid-0de37219-61f3-43fe-b3cf-29fed43ccd82 svg{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;}#mermaid-0de37219-61f3-43fe-b3cf-29fed43ccd82 p{margin:0;}#mermaid-0de37219-61f3-43fe-b3cf-29fed43ccd82 g.classGroup text{fill:#ccc;stroke:none;font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:10px;}#mermaid-0de37219-61f3-43fe-b3cf-29fed43ccd82 g.classGroup text .title{font-weight:bolder;}#mermaid-0de37219-61f3-43fe-b3cf-29fed43ccd82 .nodeLabel,#mermaid-0de37219-61f3-43fe-b3cf-29fed43ccd82 .edgeLabel{color:#e0dfdf;}#mermaid-0de37219-61f3-43fe-b3cf-29fed43ccd82 .edgeLabel .label rect{fill:#1f2020;}#mermaid-0de37219-61f3-43fe-b3cf-29fed43ccd82 .label text{fill:#e0dfdf;}#mermaid-0de37219-61f3-43fe-b3cf-29fed43ccd82 .labelBkg{background:#1f2020;}#mermaid-0de37219-61f3-43fe-b3cf-29fed43ccd82 .edgeLabel .label span{background:#1f2020;}#mermaid-0de37219-61f3-43fe-b3cf-29fed43ccd82 .classTitle{font-weight:bolder;}#mermaid-0de37219-61f3-43fe-b3cf-29fed43ccd82 .node rect,#mermaid-0de37219-61f3-43fe-b3cf-29fed43ccd82 .node circle,#mermaid-0de37219-61f3-43fe-b3cf-29fed43ccd82 .node ellipse,#mermaid-0de37219-61f3-43fe-b3cf-29fed43ccd82 .node polygon,#mermaid-0de37219-61f3-43fe-b3cf-29fed43ccd82 .node path{fill:#1f2020;stroke:#ccc;stroke-width:1px;}#mermaid-0de37219-61f3-43fe-b3cf-29fed43ccd82 .divider{stroke:#ccc;stroke-width:1;}#mermaid-0de37219-61f3-43fe-b3cf-29fed43ccd82 g.clickable{cursor:pointer;}#mermaid-0de37219-61f3-43fe-b3cf-29fed43ccd82 g.classGroup rect{fill:#1f2020;stroke:#ccc;}#mermaid-0de37219-61f3-43fe-b3cf-29fed43ccd82 g.classGroup line{stroke:#ccc;stroke-width:1;}#mermaid-0de37219-61f3-43fe-b3cf-29fed43ccd82 .classLabel .box{stroke:none;stroke-width:0;fill:#1f2020;opacity:0.5;}#mermaid-0de37219-61f3-43fe-b3cf-29fed43ccd82 .classLabel .label{fill:#ccc;font-size:10px;}#mermaid-0de37219-61f3-43fe-b3cf-29fed43ccd82 .relation{stroke:lightgrey;stroke-width:1;fill:none;}#mermaid-0de37219-61f3-43fe-b3cf-29fed43ccd82 .dashed-line{stroke-dasharray:3;}#mermaid-0de37219-61f3-43fe-b3cf-29fed43ccd82 .dotted-line{stroke-dasharray:1 2;}#mermaid-0de37219-61f3-43fe-b3cf-29fed43ccd82 #compositionStart,#mermaid-0de37219-61f3-43fe-b3cf-29fed43ccd82 .composition{fill:lightgrey!important;stroke:lightgrey!important;stroke-width:1;}#mermaid-0de37219-61f3-43fe-b3cf-29fed43ccd82 #compositionEnd,#mermaid-0de37219-61f3-43fe-b3cf-29fed43ccd82 .composition{fill:lightgrey!important;stroke:lightgrey!important;stroke-width:1;}#mermaid-0de37219-61f3-43fe-b3cf-29fed43ccd82 #dependencyStart,#mermaid-0de37219-61f3-43fe-b3cf-29fed43ccd82 .dependency{fill:lightgrey!important;stroke:lightgrey!important;stroke-width:1;}#mermaid-0de37219-61f3-43fe-b3cf-29fed43ccd82 #dependencyStart,#mermaid-0de37219-61f3-43fe-b3cf-29fed43ccd82 .dependency{fill:lightgrey!important;stroke:lightgrey!important;stroke-width:1;}#mermaid-0de37219-61f3-43fe-b3cf-29fed43ccd82 #extensionStart,#mermaid-0de37219-61f3-43fe-b3cf-29fed43ccd82 .extension{fill:transparent!important;stroke:lightgrey!important;stroke-width:1;}#mermaid-0de37219-61f3-43fe-b3cf-29fed43ccd82 #extensionEnd,#mermaid-0de37219-61f3-43fe-b3cf-29fed43ccd82 .extension{fill:transparent!important;stroke:lightgrey!important;stroke-width:1;}#mermaid-0de37219-61f3-43fe-b3cf-29fed43ccd82 #aggregationStart,#mermaid-0de37219-61f3-43fe-b3cf-29fed43ccd82 .aggregation{fill:transparent!important;stroke:lightgrey!important;stroke-width:1;}#mermaid-0de37219-61f3-43fe-b3cf-29fed43ccd82 #aggregationEnd,#mermaid-0de37219-61f3-43fe-b3cf-29fed43ccd82 .aggregation{fill:transparent!important;stroke:lightgrey!important;stroke-width:1;}#mermaid-0de37219-61f3-43fe-b3cf-29fed43ccd82 #lollipopStart,#mermaid-0de37219-61f3-43fe-b3cf-29fed43ccd82 .lollipop{fill:#1f2020!important;stroke:lightgrey!important;stroke-width:1;}#mermaid-0de37219-61f3-43fe-b3cf-29fed43ccd82 #lollipopEnd,#mermaid-0de37219-61f3-43fe-b3cf-29fed43ccd82 .lollipop{fill:#1f2020!important;stroke:lightgrey!important;stroke-width:1;}#mermaid-0de37219-61f3-43fe-b3cf-29fed43ccd82 .edgeTerminals{font-size:11px;line-height:initial;}#mermaid-0de37219-61f3-43fe-b3cf-29fed43ccd82 .classTitleText{text-anchor:middle;font-size:18px;fill:#ccc;}#mermaid-0de37219-61f3-43fe-b3cf-29fed43ccd82 :root{--mermaid-font-family:"trebuchet ms",verdana,arial,sans-serif;}</style><g><defs><marker orient="auto" markerHeight="240" markerWidth="190" refY="7" refX="18" class="marker aggregation class" id="mermaid-0de37219-61f3-43fe-b3cf-29fed43ccd82_class-aggregationStart"><path d="M 18,7 L9,13 L1,7 L9,1 Z"></path></marker></defs><defs><marker orient="auto" markerHeight="28" markerWidth="20" refY="7" refX="1" class="marker aggregation class" id="mermaid-0de37219-61f3-43fe-b3cf-29fed43ccd82_class-aggregationEnd"><path d="M 18,7 L9,13 L1,7 L9,1 Z"></path></marker></defs><defs><marker orient="auto" markerHeight="240" markerWidth="190" refY="7" refX="18" class="marker extension class" id="mermaid-0de37219-61f3-43fe-b3cf-29fed43ccd82_class-extensionStart"><path d="M 1,7 L18,13 V 1 Z"></path></marker></defs><defs><marker orient="auto" markerHeight="28" markerWidth="20" refY="7" refX="1" class="marker extension class" id="mermaid-0de37219-61f3-43fe-b3cf-29fed43ccd82_class-extensionEnd"><path d="M 1,1 V 13 L18,7 Z"></path></marker></defs><defs><marker orient="auto" markerHeight="240" markerWidth="190" refY="7" refX="18" class="marker composition class" id="mermaid-0de37219-61f3-43fe-b3cf-29fed43ccd82_class-compositionStart"><path d="M 18,7 L9,13 L1,7 L9,1 Z"></path></marker></defs><defs><marker orient="auto" markerHeight="28" markerWidth="20" refY="7" refX="1" class="marker composition class" id="mermaid-0de37219-61f3-43fe-b3cf-29fed43ccd82_class-compositionEnd"><path d="M 18,7 L9,13 L1,7 L9,1 Z"></path></marker></defs><defs><marker orient="auto" markerHeight="240" markerWidth="190" refY="7" refX="6" class="marker dependency class" id="mermaid-0de37219-61f3-43fe-b3cf-29fed43ccd82_class-dependencyStart"><path d="M 5,7 L9,13 L1,7 L9,1 Z"></path></marker></defs><defs><marker orient="auto" markerHeight="28" markerWidth="20" refY="7" refX="13" class="marker dependency class" id="mermaid-0de37219-61f3-43fe-b3cf-29fed43ccd82_class-dependencyEnd"><path d="M 18,7 L9,13 L14,7 L9,1 Z"></path></marker></defs><defs><marker orient="auto" markerHeight="240" markerWidth="190" refY="7" refX="13" class="marker lollipop class" id="mermaid-0de37219-61f3-43fe-b3cf-29fed43ccd82_class-lollipopStart"><circle r="6" cy="7" cx="7" fill="transparent" stroke="black"></circle></marker></defs><defs><marker orient="auto" markerHeight="240" markerWidth="190" refY="7" refX="1" class="marker lollipop class" id="mermaid-0de37219-61f3-43fe-b3cf-29fed43ccd82_class-lollipopEnd"><circle r="6" cy="7" cx="7" fill="transparent" stroke="black"></circle></marker></defs><g class="root"><g class="clusters"></g><g class="edgePaths"><path marker-start="url(#mermaid-0de37219-61f3-43fe-b3cf-29fed43ccd82_class-extensionStart)" style=";" class="edge-thickness-normal edge-pattern-solid relation" id="id_ModuleTemp_MemoryMod_1" d="M689.254,611.519L798.718,641.432C908.181,671.346,1127.109,731.173,1236.572,787.253C1346.036,843.333,1346.036,895.667,1346.036,948C1346.036,1000.333,1346.036,1052.667,1346.036,1107C1346.036,1161.333,1346.036,1217.667,1346.036,1274C1346.036,1330.333,1346.036,1386.667,1348.961,1421C1351.885,1455.333,1357.734,1467.667,1360.659,1473.833L1363.584,1480"></path><path marker-start="url(#mermaid-0de37219-61f3-43fe-b3cf-29fed43ccd82_class-extensionStart)" style=";" class="edge-thickness-normal edge-pattern-solid relation" id="id_ModuleTemp_VPU_2" d="M415.66,636.366L359.336,662.138C303.011,687.911,190.362,739.455,135.97,777.394C81.579,815.333,85.446,839.667,87.379,851.833L89.312,864"></path><path marker-start="url(#mermaid-0de37219-61f3-43fe-b3cf-29fed43ccd82_class-extensionStart)" style=";" class="edge-thickness-normal edge-pattern-solid relation" id="id_ModuleTemp_CIMCluster_3" d="M417.486,672.085L390.314,691.904C363.142,711.723,308.798,751.362,288.225,781.348C267.652,811.333,280.85,831.667,287.448,841.833L294.047,852"></path><path marker-start="url(#mermaid-0de37219-61f3-43fe-b3cf-29fed43ccd82_class-extensionStart)" style=";" class="edge-thickness-normal edge-pattern-solid relation" id="id_ModuleTemp_LoadStoreUnit_4" d="M528.327,723.78L526.559,734.983C524.791,746.187,521.256,768.593,529.191,793.463C537.127,818.333,556.534,845.667,566.237,859.333L575.94,873"></path><path marker-start="url(#mermaid-0de37219-61f3-43fe-b3cf-29fed43ccd82_class-extensionStart)" style=";" class="edge-thickness-normal edge-pattern-solid relation" id="id_ModuleTemp_ManageMod_5" d="M685.201,695.296L702.723,711.246C720.245,727.197,755.288,759.099,782.494,788.716C809.699,818.333,829.067,845.667,838.751,859.333L848.435,873"></path><path marker-start="url(#mermaid-0de37219-61f3-43fe-b3cf-29fed43ccd82_class-extensionStart)" style=";" class="edge-thickness-normal edge-pattern-solid relation" id="id_ModuleTemp_NoCRouter_6" d="M688.782,624.388L764.184,652.157C839.586,679.925,990.391,735.463,1068.78,773.398C1147.17,811.333,1153.145,831.667,1156.132,841.833L1159.12,852"></path><path marker-start="url(#mermaid-0de37219-61f3-43fe-b3cf-29fed43ccd82_class-extensionStart)" style=";" class="edge-thickness-normal edge-pattern-solid relation" id="id_MemoryMod_DRAMBank_7" d="M1547.674,1663.193L1575.114,1676.828C1602.554,1690.462,1657.434,1717.731,1691.063,1737.532C1724.693,1757.333,1737.071,1769.667,1743.26,1775.833L1749.45,1782"></path><path marker-start="url(#mermaid-0de37219-61f3-43fe-b3cf-29fed43ccd82_class-extensionStart)" style=";" class="edge-thickness-normal edge-pattern-solid relation" id="id_MemoryMod_SRAMBank_8" d="M1420.495,1738L1420.495,1739.167C1420.495,1740.333,1420.495,1742.667,1425.018,1750C1429.54,1757.333,1438.584,1769.667,1443.106,1775.833L1447.629,1782"></path><path marker-start="url(#mermaid-0de37219-61f3-43fe-b3cf-29fed43ccd82_class-extensionStart)" style=";" class="edge-thickness-normal edge-pattern-solid relation" id="id_MemoryMod_CIMPage_9" d="M1291.595,1617.24L1132.388,1638.533C973.182,1659.827,654.769,1702.413,495.907,1727.873C337.045,1753.333,337.734,1761.667,338.078,1765.833L338.422,1770"></path><path marker-start="url(#mermaid-0de37219-61f3-43fe-b3cf-29fed43ccd82_class-compositionStart)" style=";" class="edge-thickness-normal edge-pattern-solid relation" id="id_PNMDie_NPUCore_10" d="M1344.957,321.817L1352.08,327.681C1359.202,333.545,1373.447,345.272,1380.57,357.303C1387.692,369.333,1387.692,381.667,1387.692,387.833L1387.692,394"></path><path marker-start="url(#mermaid-0de37219-61f3-43fe-b3cf-29fed43ccd82_class-compositionStart)" style=";" class="edge-thickness-normal edge-pattern-solid relation" id="id_PNMDie_Mesh2D_11" d="M1057.096,335.704L1055.108,339.254C1053.12,342.803,1049.145,349.901,1047.157,389.617C1045.169,429.333,1045.169,501.667,1045.169,574C1045.169,646.333,1045.169,718.667,1045.169,781C1045.169,843.333,1045.169,895.667,1045.169,948C1045.169,1000.333,1045.169,1052.667,1048.604,1087C1052.038,1121.333,1058.908,1137.667,1062.343,1145.833L1065.778,1154"></path><path marker-start="url(#mermaid-0de37219-61f3-43fe-b3cf-29fed43ccd82_class-compositionStart)" style=";" class="edge-thickness-normal edge-pattern-solid relation" id="id_NPUCore_DRAMBank_12" d="M1549.159,650.827L1598.258,674.189C1647.358,697.551,1745.558,744.276,1794.658,793.805C1843.758,843.333,1843.758,895.667,1843.758,948C1843.758,1000.333,1843.758,1052.667,1843.758,1107C1843.758,1161.333,1843.758,1217.667,1843.758,1274C1843.758,1330.333,1843.758,1386.667,1843.758,1441C1843.758,1495.333,1843.758,1547.667,1843.758,1598C1843.758,1648.333,1843.758,1696.667,1843.248,1727C1842.739,1757.333,1841.719,1769.667,1841.21,1775.833L1840.7,1782"></path><path marker-start="url(#mermaid-0de37219-61f3-43fe-b3cf-29fed43ccd82_class-compositionStart)" style=";" class="edge-thickness-normal edge-pattern-solid relation" id="id_NPUCore_SRAMBank_13" d="M1547.221,695.582L1568.087,711.485C1588.954,727.388,1630.687,759.194,1651.554,801.264C1672.42,843.333,1672.42,895.667,1672.42,948C1672.42,1000.333,1672.42,1052.667,1672.42,1107C1672.42,1161.333,1672.42,1217.667,1672.42,1274C1672.42,1330.333,1672.42,1386.667,1672.42,1441C1672.42,1495.333,1672.42,1547.667,1672.42,1598C1672.42,1648.333,1672.42,1696.667,1662.919,1727.879C1653.417,1759.09,1634.413,1773.181,1624.911,1780.226L1615.409,1787.271"></path><path marker-start="url(#mermaid-0de37219-61f3-43fe-b3cf-29fed43ccd82_class-compositionStart)" style=";" class="edge-thickness-normal edge-pattern-solid relation" id="id_NPUCore_VPU_14" d="M1224.775,603.881L1054.739,635.067C884.704,666.254,544.632,728.627,366.7,771.98C188.767,815.333,172.974,839.667,165.077,851.833L157.18,864"></path><path marker-start="url(#mermaid-0de37219-61f3-43fe-b3cf-29fed43ccd82_class-compositionStart)" style=";" class="edge-thickness-normal edge-pattern-solid relation" id="id_NPUCore_CIMCluster_15" d="M1224.961,612.389L1098.772,642.158C972.583,671.926,720.205,731.463,586.797,771.398C453.39,811.333,438.953,831.667,431.735,841.833L424.516,852"></path><path marker-start="url(#mermaid-0de37219-61f3-43fe-b3cf-29fed43ccd82_class-compositionStart)" style=";" class="edge-thickness-normal edge-pattern-solid relation" id="id_NPUCore_LoadStoreUnit_16" d="M1225.413,628.406L1144.584,655.505C1063.755,682.604,902.096,736.802,811.583,777.568C721.07,818.333,701.702,845.667,692.018,859.333L682.334,873"></path><path marker-start="url(#mermaid-0de37219-61f3-43fe-b3cf-29fed43ccd82_class-compositionStart)" style=";" class="edge-thickness-normal edge-pattern-solid relation" id="id_NPUCore_ManageMod_17" d="M1226.238,651.135L1177.445,674.446C1128.652,697.756,1031.066,744.378,979.496,781.356C927.926,818.333,922.372,845.667,919.595,859.333L916.818,873"></path><path marker-start="url(#mermaid-0de37219-61f3-43fe-b3cf-29fed43ccd82_class-compositionStart)" style=";" class="edge-thickness-normal edge-pattern-solid relation" id="id_NPUCore_NoCRouter_18" d="M1249.331,768.672L1246.686,772.393C1244.041,776.114,1238.751,783.557,1233.119,797.445C1227.486,811.333,1221.511,831.667,1218.524,841.833L1215.537,852"></path><path marker-start="url(#mermaid-0de37219-61f3-43fe-b3cf-29fed43ccd82_class-compositionStart)" style=";" class="edge-thickness-normal edge-pattern-solid relation" id="id_CIMCluster_CIMPage_19" d="M356.356,1062L356.356,1069.167C356.356,1076.333,356.356,1090.667,356.356,1126C356.356,1161.333,356.356,1217.667,356.356,1274C356.356,1330.333,356.356,1386.667,356.356,1441C356.356,1495.333,356.356,1547.667,356.356,1598C356.356,1648.333,356.356,1696.667,356.012,1725C355.668,1753.333,354.979,1761.667,354.634,1765.833L354.29,1770"></path><path marker-start="url(#mermaid-0de37219-61f3-43fe-b3cf-29fed43ccd82_class-compositionStart)" style=";" class="edge-thickness-normal edge-pattern-solid relation" id="id_Mesh2D_Node2DMesh_20" d="M1116.248,1412L1116.248,1417.167C1116.248,1422.333,1116.248,1432.667,1116.248,1448C1116.248,1463.333,1116.248,1483.667,1116.248,1493.833L1116.248,1504"></path><path marker-end="url(#mermaid-0de37219-61f3-43fe-b3cf-29fed43ccd82_class-dependencyEnd)" style=";" class="edge-thickness-normal edge-pattern-solid relation" id="id_NPUCore_Primitive_21" d="M1476.666,754L1479.714,760.167C1482.762,766.333,1488.858,778.667,1491.907,790C1494.955,801.333,1494.955,811.667,1494.955,816.833L1494.955,822"></path><path marker-end="url(#mermaid-0de37219-61f3-43fe-b3cf-29fed43ccd82_class-dependencyEnd)" style=";" class="edge-thickness-normal edge-pattern-solid relation" id="id_Primitive_TensorInfo_22" d="M1494.955,1068L1494.955,1074.167C1494.955,1080.333,1494.955,1092.667,1494.955,1104C1494.955,1115.333,1494.955,1125.667,1494.955,1130.833L1494.955,1136"></path><path marker-end="url(#mermaid-0de37219-61f3-43fe-b3cf-29fed43ccd82_class-dependencyEnd)" style=";" class="edge-thickness-normal edge-pattern-solid relation" id="id_TensorInfo_MemoryMod_23" d="M1494.955,1406L1494.955,1412.167C1494.955,1418.333,1494.955,1430.667,1492.459,1442.096C1489.962,1453.526,1484.97,1464.053,1482.474,1469.316L1479.978,1474.579"></path><path marker-end="url(#mermaid-0de37219-61f3-43fe-b3cf-29fed43ccd82_class-dependencyEnd)" style=";" class="edge-thickness-normal edge-pattern-solid relation" id="id_NoCRouter_Mesh2D_24" d="M1187.328,1044L1187.328,1054.167C1187.328,1064.333,1187.328,1084.667,1184.281,1102.078C1181.234,1119.49,1175.14,1133.98,1172.093,1141.224L1169.045,1148.469"></path></g><g class="edgeLabels"><g class="edgeLabel"><g transform="translate(0, -12)" class="label"><foreignObject height="24" width="0"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel" style=";display: inline-block"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, -12)" class="label"><foreignObject height="24" width="0"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel" style=";display: inline-block"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, -12)" class="label"><foreignObject height="24" width="0"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel" style=";display: inline-block"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, -12)" class="label"><foreignObject height="24" width="0"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel" style=";display: inline-block"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, -12)" class="label"><foreignObject height="24" width="0"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel" style=";display: inline-block"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, -12)" class="label"><foreignObject height="24" width="0"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel" style=";display: inline-block"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, -12)" class="label"><foreignObject height="24" width="0"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel" style=";display: inline-block"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, -12)" class="label"><foreignObject height="24" width="0"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel" style=";display: inline-block"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, -12)" class="label"><foreignObject height="24" width="0"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel" style=";display: inline-block"></span></div></foreignObject></g></g><g transform="translate(1387.6922073364258, 357)" class="edgeLabel"><g transform="translate(-29.89375114440918, -12)" class="label"><foreignObject height="24" width="59.78750228881836"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel" style=";display: inline-block"><p>contains</p></span></div></foreignObject></g></g><g transform="translate(1045.1687622070312, 791)" class="edgeLabel"><g transform="translate(-29.89375114440918, -12)" class="label"><foreignObject height="24" width="59.78750228881836"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel" style=";display: inline-block"><p>contains</p></span></div></foreignObject></g></g><g transform="translate(1843.7578353881836, 1274)" class="edgeLabel"><g transform="translate(-29.89375114440918, -12)" class="label"><foreignObject height="24" width="59.78750228881836"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel" style=";display: inline-block"><p>contains</p></span></div></foreignObject></g></g><g transform="translate(1672.420331954956, 1274)" class="edgeLabel"><g transform="translate(-29.89375114440918, -12)" class="label"><foreignObject height="24" width="59.78750228881836"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel" style=";display: inline-block"><p>contains</p></span></div></foreignObject></g></g><g transform="translate(204.56094074249268, 791)" class="edgeLabel"><g transform="translate(-29.89375114440918, -12)" class="label"><foreignObject height="24" width="59.78750228881836"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel" style=";display: inline-block"><p>contains</p></span></div></foreignObject></g></g><g transform="translate(467.8265657424927, 791)" class="edgeLabel"><g transform="translate(-29.89375114440918, -12)" class="label"><foreignObject height="24" width="59.78750228881836"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel" style=";display: inline-block"><p>contains</p></span></div></foreignObject></g></g><g transform="translate(740.4375047683716, 791)" class="edgeLabel"><g transform="translate(-29.89375114440918, -12)" class="label"><foreignObject height="24" width="59.78750228881836"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel" style=";display: inline-block"><p>contains</p></span></div></foreignObject></g></g><g transform="translate(933.4796962738037, 791)" class="edgeLabel"><g transform="translate(-29.89375114440918, -12)" class="label"><foreignObject height="24" width="59.78750228881836"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel" style=";display: inline-block"><p>contains</p></span></div></foreignObject></g></g><g transform="translate(1233.4609537124634, 791)" class="edgeLabel"><g transform="translate(-29.89375114440918, -12)" class="label"><foreignObject height="24" width="59.78750228881836"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel" style=";display: inline-block"><p>contains</p></span></div></foreignObject></g></g><g transform="translate(356.3562545776367, 1443)" class="edgeLabel"><g transform="translate(-29.89375114440918, -12)" class="label"><foreignObject height="24" width="59.78750228881836"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel" style=";display: inline-block"><p>contains</p></span></div></foreignObject></g></g><g transform="translate(1116.2484512329102, 1443)" class="edgeLabel"><g transform="translate(-29.89375114440918, -12)" class="label"><foreignObject height="24" width="59.78750228881836"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel" style=";display: inline-block"><p>contains</p></span></div></foreignObject></g></g><g transform="translate(1494.954704284668, 791)" class="edgeLabel"><g transform="translate(-34.26250076293945, -12)" class="label"><foreignObject height="24" width="68.5250015258789"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel" style=";display: inline-block"><p>processes</p></span></div></foreignObject></g></g><g transform="translate(1494.954704284668, 1105)" class="edgeLabel"><g transform="translate(-38.20000076293945, -12)" class="label"><foreignObject height="24" width="76.4000015258789"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel" style=";display: inline-block"><p>references</p></span></div></foreignObject></g></g><g transform="translate(1494.954704284668, 1443)" class="edgeLabel"><g transform="translate(-33.48125076293945, -12)" class="label"><foreignObject height="24" width="66.9625015258789"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel" style=";display: inline-block"><p>stored_in</p></span></div></foreignObject></g></g><g transform="translate(1187.328140258789, 1105)" class="edgeLabel"><g transform="translate(-15.21250057220459, -12)" class="label"><foreignObject height="24" width="30.42500114440918"><div class="labelBkg" xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel" style=";display: inline-block"><p>uses</p></span></div></foreignObject></g></g></g><g class="nodes"><g transform="translate(551.9593734741211, 574)" id="classId-ModuleTemp-48" class="node default"><g class="basic label-container"><path style="" fill="#1f2020" stroke-width="0" stroke="none" d="M-119.93125915527344 -132 L119.93125915527344 -132 L119.93125915527344 132 L-119.93125915527344 132"></path><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-119.93125915527344 -132 C-57.00642667125101 -132, 5.918405812771411 -132, 119.93125915527344 -132 M-119.93125915527344 -132 C-30.140637719688186 -132, 59.649983715897065 -132, 119.93125915527344 -132 M119.93125915527344 -132 C119.93125915527344 -64.03620406739527, 119.93125915527344 3.9275918652094504, 119.93125915527344 132 M119.93125915527344 -132 C119.93125915527344 -38.512052096831425, 119.93125915527344 54.97589580633715, 119.93125915527344 132 M119.93125915527344 132 C60.091071728079 132, 0.2508843008845645 132, -119.93125915527344 132 M119.93125915527344 132 C33.7451014953691 132, -52.44105616453524 132, -119.93125915527344 132 M-119.93125915527344 132 C-119.93125915527344 51.02678047922004, -119.93125915527344 -29.946439041559927, -119.93125915527344 -132 M-119.93125915527344 132 C-119.93125915527344 68.90386450545748, -119.93125915527344 5.807729010914954, -119.93125915527344 -132"></path></g><g transform="translate(-37.90625, -108)" class="annotation-group text"><g transform="translate(0,-12)" style="" class="label"><foreignObject height="24" width="75.8125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 126px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>«abstract»</p></span></div></foreignObject></g></g><g transform="translate(-46.962501525878906, -84)" class="label-group text"><g transform="translate(0,-12)" style="font-weight: bolder" class="label"><foreignObject height="24" width="93.92500305175781"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 147px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>ModuleTemp</p></span></div></foreignObject></g></g><g transform="translate(-107.93125915527344, -36)" class="members-group text"><g transform="translate(0,-12)" style="" class="label"><foreignObject height="24" width="77.2874984741211"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 136px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+name: str</p></span></div></foreignObject></g><g transform="translate(0,12)" style="" class="label"><foreignObject height="24" width="52.212501525878906"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 108px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+id: int</p></span></div></foreignObject></g><g transform="translate(0,36)" style="" class="label"><foreignObject height="24" width="121.75"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 180px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+state: ModState</p></span></div></foreignObject></g><g transform="translate(0,60)" style="" class="label"><foreignObject height="24" width="168.90000915527344"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 232px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+parent_core: NPUCore</p></span></div></foreignObject></g></g><g transform="translate(-107.93125915527344, 84)" class="methods-group text"><g transform="translate(0,-12)" style="font-style:italic;" class="label"><foreignObject height="24" width="153.33750915527344"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 213px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+run_prim(primitive)</p></span></div></foreignObject></g><g transform="translate(0,12)" style="font-style:italic;" class="label"><foreignObject height="24" width="143.0500030517578"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 203px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+calculate_energy()</p></span></div></foreignObject></g></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-119.93125915527344 -60 C-65.88520069089479 -60, -11.839142226516145 -60, 119.93125915527344 -60 M-119.93125915527344 -60 C-63.229514695984086 -60, -6.527770236694735 -60, 119.93125915527344 -60"></path></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-119.93125915527344 60 C-32.4849753802543 60, 54.96130839476484 60, 119.93125915527344 60 M-119.93125915527344 60 C-65.81562124574401 60, -11.699983336214572 60, 119.93125915527344 60"></path></g></g><g transform="translate(1420.4953308105469, 1600)" id="classId-MemoryMod-49" class="node default"><g class="basic label-container"><path style="" fill="#1f2020" stroke-width="0" stroke="none" d="M-111.05937957763672 -120 L111.05937957763672 -120 L111.05937957763672 120 L-111.05937957763672 120"></path><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-111.05937957763672 -120 C-54.21144601709322 -120, 2.636487543450272 -120, 111.05937957763672 -120 M-111.05937957763672 -120 C-54.191159728818405 -120, 2.677060119999908 -120, 111.05937957763672 -120 M111.05937957763672 -120 C111.05937957763672 -40.16754058223489, 111.05937957763672 39.664918835530216, 111.05937957763672 120 M111.05937957763672 -120 C111.05937957763672 -24.561030742587505, 111.05937957763672 70.87793851482499, 111.05937957763672 120 M111.05937957763672 120 C22.37689821301052 120, -66.30558315161568 120, -111.05937957763672 120 M111.05937957763672 120 C62.829223473418104 120, 14.599067369199489 120, -111.05937957763672 120 M-111.05937957763672 120 C-111.05937957763672 59.620607720731435, -111.05937957763672 -0.7587845585371298, -111.05937957763672 -120 M-111.05937957763672 120 C-111.05937957763672 57.47308452873061, -111.05937957763672 -5.053830942538781, -111.05937957763672 -120"></path></g><g transform="translate(-37.90625, -96)" class="annotation-group text"><g transform="translate(0,-12)" style="" class="label"><foreignObject height="24" width="75.8125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 126px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>«abstract»</p></span></div></foreignObject></g></g><g transform="translate(-44.78125, -72)" class="label-group text"><g transform="translate(0,-12)" style="font-weight: bolder" class="label"><foreignObject height="24" width="89.5625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 144px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>MemoryMod</p></span></div></foreignObject></g></g><g transform="translate(-99.05937957763672, -24)" class="members-group text"><g transform="translate(0,-12)" style="" class="label"><foreignObject height="24" width="149.8000030517578"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 212px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+address_space: dict</p></span></div></foreignObject></g><g transform="translate(0,12)" style="" class="label"><foreignObject height="24" width="121.73750305175781"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 180px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+bit_width: float</p></span></div></foreignObject></g><g transform="translate(0,36)" style="" class="label"><foreignObject height="24" width="121.0625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 181px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+op_energy: dict</p></span></div></foreignObject></g><g transform="translate(0,60)" style="" class="label"><foreignObject height="24" width="93.42500305175781"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 150px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+record: dict</p></span></div></foreignObject></g></g><g transform="translate(-99.05937957763672, 96)" class="methods-group text"><g transform="translate(0,-12)" style="font-style:italic;" class="label"><foreignObject height="24" width="153.33750915527344"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 213px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+run_prim(primitive)</p></span></div></foreignObject></g></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-111.05937957763672 -48 C-62.082972639507574 -48, -13.106565701378429 -48, 111.05937957763672 -48 M-111.05937957763672 -48 C-66.25891966482959 -48, -21.458459752022463 -48, 111.05937957763672 -48"></path></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-111.05937957763672 72 C-39.67000247096662 72, 31.719374635703474 72, 111.05937957763672 72 M-111.05937957763672 72 C-34.90087056814717 72, 41.25763844134238 72, 111.05937957763672 72"></path></g></g><g transform="translate(1153.2672023773193, 164)" id="classId-PNMDie-50" class="node default"><g class="basic label-container"><path style="" fill="#1f2020" stroke-width="0" stroke="none" d="M-177.7937469482422 -156 L177.7937469482422 -156 L177.7937469482422 156 L-177.7937469482422 156"></path><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-177.7937469482422 -156 C-99.19220413139855 -156, -20.59066131455492 -156, 177.7937469482422 -156 M-177.7937469482422 -156 C-54.36559905507865 -156, 69.0625488380849 -156, 177.7937469482422 -156 M177.7937469482422 -156 C177.7937469482422 -74.96206406981656, 177.7937469482422 6.075871860366874, 177.7937469482422 156 M177.7937469482422 -156 C177.7937469482422 -37.000111318799114, 177.7937469482422 81.99977736240177, 177.7937469482422 156 M177.7937469482422 156 C67.94758748104685 156, -41.89857198614848 156, -177.7937469482422 156 M177.7937469482422 156 C40.164828571688446 156, -97.4640898048653 156, -177.7937469482422 156 M-177.7937469482422 156 C-177.7937469482422 92.4282648282512, -177.7937469482422 28.85652965650239, -177.7937469482422 -156 M-177.7937469482422 156 C-177.7937469482422 60.113643915248346, -177.7937469482422 -35.77271216950331, -177.7937469482422 -156"></path></g><g transform="translate(0, -132)" class="annotation-group text"></g><g transform="translate(-28.125, -132)" class="label-group text"><g transform="translate(0,-12)" style="font-weight: bolder" class="label"><foreignObject height="24" width="56.25"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 109px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>PNMDie</p></span></div></foreignObject></g></g><g transform="translate(-165.7937469482422, -84)" class="members-group text"><g transform="translate(0,-12)" style="" class="label"><foreignObject height="24" width="65.45000457763672"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 122px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+row: int</p></span></div></foreignObject></g><g transform="translate(0,12)" style="" class="label"><foreignObject height="24" width="59.962501525878906"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 116px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+col: int</p></span></div></foreignObject></g><g transform="translate(0,36)" style="" class="label"><foreignObject height="24" width="97.8375015258789"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 160px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+noc: Mesh2D</p></span></div></foreignObject></g><g transform="translate(0,60)" style="" class="label"><foreignObject height="24" width="177.9250030517578"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 241px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+npu_cores: NPUCore[][]</p></span></div></foreignObject></g><g transform="translate(0,84)" style="" class="label"><foreignObject height="24" width="303.4624938964844"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 380px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+golden_vm_system: GOLDEN_VM_SYSTEM</p></span></div></foreignObject></g><g transform="translate(0,108)" style="" class="label"><foreignObject height="24" width="137.22500610351562"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 195px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+current_cycle: int</p></span></div></foreignObject></g></g><g transform="translate(-165.7937469482422, 84)" class="methods-group text"><g transform="translate(0,-12)" style="" class="label"><foreignObject height="24" width="168.16250610351562"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 234px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+run_simulation_until()</p></span></div></foreignObject></g><g transform="translate(0,12)" style="" class="label"><foreignObject height="24" width="186.53750610351562"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 249px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+execute_until(end_time)</p></span></div></foreignObject></g><g transform="translate(0,36)" style="" class="label"><foreignObject height="24" width="129.33750915527344"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 193px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+end_simulation()</p></span></div></foreignObject></g></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-177.7937469482422 -108 C-84.95398457986337 -108, 7.8857777885154405 -108, 177.7937469482422 -108 M-177.7937469482422 -108 C-47.2563838273407 -108, 83.28097929356079 -108, 177.7937469482422 -108"></path></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-177.7937469482422 60 C-50.78366254655167 60, 76.22642185513885 60, 177.7937469482422 60 M-177.7937469482422 60 C-81.45699298063325 60, 14.879760986975697 60, 177.7937469482422 60"></path></g></g><g transform="translate(1387.6922073364258, 574)" id="classId-NPUCore-51" class="node default"><g class="basic label-container"><path style="" fill="#1f2020" stroke-width="0" stroke="none" d="M-145.21250915527344 -180 L145.21250915527344 -180 L145.21250915527344 180 L-145.21250915527344 180"></path><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-145.21250915527344 -180 C-72.90273447190553 -180, -0.5929597885376268 -180, 145.21250915527344 -180 M-145.21250915527344 -180 C-80.82571019372806 -180, -16.438911232182676 -180, 145.21250915527344 -180 M145.21250915527344 -180 C145.21250915527344 -47.744973676092144, 145.21250915527344 84.51005264781571, 145.21250915527344 180 M145.21250915527344 -180 C145.21250915527344 -88.19799188363803, 145.21250915527344 3.60401623272395, 145.21250915527344 180 M145.21250915527344 180 C39.332732525609245 180, -66.54704410405495 180, -145.21250915527344 180 M145.21250915527344 180 C49.81672644125068 180, -45.57905627277208 180, -145.21250915527344 180 M-145.21250915527344 180 C-145.21250915527344 93.81882857378113, -145.21250915527344 7.637657147562265, -145.21250915527344 -180 M-145.21250915527344 180 C-145.21250915527344 82.6420260019673, -145.21250915527344 -14.71594799606541, -145.21250915527344 -180"></path></g><g transform="translate(0, -156)" class="annotation-group text"></g><g transform="translate(-32.900001525878906, -156)" class="label-group text"><g transform="translate(0,-12)" style="font-weight: bolder" class="label"><foreignObject height="24" width="65.80000305175781"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 118px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>NPUCore</p></span></div></foreignObject></g></g><g transform="translate(-133.21250915527344, -108)" class="members-group text"><g transform="translate(0,-12)" style="" class="label"><foreignObject height="24" width="46.75"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 102px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+x: int</p></span></div></foreignObject></g><g transform="translate(0,12)" style="" class="label"><foreignObject height="24" width="46.625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 102px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+y: int</p></span></div></foreignObject></g><g transform="translate(0,36)" style="" class="label"><foreignObject height="24" width="52.212501525878906"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 108px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+id: int</p></span></div></foreignObject></g><g transform="translate(0,60)" style="" class="label"><foreignObject height="24" width="79.23750305175781"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 138px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+group: int</p></span></div></foreignObject></g><g transform="translate(0,84)" style="" class="label"><foreignObject height="24" width="119"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 180px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+prim_pool: dict</p></span></div></foreignObject></g><g transform="translate(0,108)" style="" class="label"><foreignObject height="24" width="190.85000610351562"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 259px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+dram_banks: DRAMBank[]</p></span></div></foreignObject></g><g transform="translate(0,132)" style="" class="label"><foreignObject height="24" width="186.3000030517578"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 255px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+sram_banks: SRAMBank[]</p></span></div></foreignObject></g><g transform="translate(0,156)" style="" class="label"><foreignObject height="24" width="213.25"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 279px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+vector_processing_unit: VPU</p></span></div></foreignObject></g><g transform="translate(0,180)" style="" class="label"><foreignObject height="24" width="178.5625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 241px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+cim_cluster: CIMCluster</p></span></div></foreignObject></g></g><g transform="translate(-133.21250915527344, 132)" class="methods-group text"><g transform="translate(0,-12)" style="" class="label"><foreignObject height="24" width="190.1875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 255px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+update(simulation_cycle)</p></span></div></foreignObject></g><g transform="translate(0,12)" style="" class="label"><foreignObject height="24" width="233.52500915527344"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 300px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+prim_receive(dispatched_prim)</p></span></div></foreignObject></g></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-145.21250915527344 -132 C-53.73279659996999 -132, 37.74691595533346 -132, 145.21250915527344 -132 M-145.21250915527344 -132 C-29.21109351535158 -132, 86.79032212457028 -132, 145.21250915527344 -132"></path></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-145.21250915527344 108 C-67.82154130649272 108, 9.569426542288 108, 145.21250915527344 108 M-145.21250915527344 108 C-32.88882219859826 108, 79.43486475807691 108, 145.21250915527344 108"></path></g></g><g transform="translate(1833.7578353881836, 1866)" id="classId-DRAMBank-52" class="node default"><g class="basic label-container"><path style="" fill="#1f2020" stroke-width="0" stroke="none" d="M-106.70625305175781 -84 L106.70625305175781 -84 L106.70625305175781 84 L-106.70625305175781 84"></path><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-106.70625305175781 -84 C-49.98588463719325 -84, 6.7344837773713095 -84, 106.70625305175781 -84 M-106.70625305175781 -84 C-55.74097084688167 -84, -4.775688642005534 -84, 106.70625305175781 -84 M106.70625305175781 -84 C106.70625305175781 -45.15945143867417, 106.70625305175781 -6.318902877348336, 106.70625305175781 84 M106.70625305175781 -84 C106.70625305175781 -48.777433422845206, 106.70625305175781 -13.554866845690412, 106.70625305175781 84 M106.70625305175781 84 C27.033928030987155 84, -52.6383969897835 84, -106.70625305175781 84 M106.70625305175781 84 C45.060952845273036 84, -16.58434736121174 84, -106.70625305175781 84 M-106.70625305175781 84 C-106.70625305175781 20.068477006903223, -106.70625305175781 -43.863045986193555, -106.70625305175781 -84 M-106.70625305175781 84 C-106.70625305175781 32.33113049317231, -106.70625305175781 -19.33773901365538, -106.70625305175781 -84"></path></g><g transform="translate(0, -60)" class="annotation-group text"></g><g transform="translate(-39.1875, -60)" class="label-group text"><g transform="translate(0,-12)" style="font-weight: bolder" class="label"><foreignObject height="24" width="78.375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 133px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>DRAMBank</p></span></div></foreignObject></g></g><g transform="translate(-94.70625305175781, -12)" class="members-group text"><g transform="translate(0,-12)" style="" class="label"><foreignObject height="24" width="118.3125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 177px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+buffer: ndarray</p></span></div></foreignObject></g></g><g transform="translate(-94.70625305175781, 36)" class="methods-group text"><g transform="translate(0,-12)" style="" class="label"><foreignObject height="24" width="150.22500610351562"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 213px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+run_prim(primitive)</p></span></div></foreignObject></g><g transform="translate(0,12)" style="" class="label"><foreignObject height="24" width="142.77500915527344"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 203px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+calculate_energy()</p></span></div></foreignObject></g></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-106.70625305175781 -36 C-42.13880104900878 -36, 22.42865095374026 -36, 106.70625305175781 -36 M-106.70625305175781 -36 C-51.651923440863044 -36, 3.4024061700317247 -36, 106.70625305175781 -36"></path></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-106.70625305175781 12 C-25.09744872398619 12, 56.51135560378543 12, 106.70625305175781 12 M-106.70625305175781 12 C-41.02433080096158 12, 24.657591449834655 12, 106.70625305175781 12"></path></g></g><g transform="translate(1509.228144645691, 1866)" id="classId-SRAMBank-53" class="node default"><g class="basic label-container"><path style="" fill="#1f2020" stroke-width="0" stroke="none" d="M-106.1812515258789 -84 L106.1812515258789 -84 L106.1812515258789 84 L-106.1812515258789 84"></path><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-106.1812515258789 -84 C-25.85103936419233 -84, 54.47917279749424 -84, 106.1812515258789 -84 M-106.1812515258789 -84 C-51.03163483545008 -84, 4.117981854978751 -84, 106.1812515258789 -84 M106.1812515258789 -84 C106.1812515258789 -31.08788194654847, 106.1812515258789 21.824236106903058, 106.1812515258789 84 M106.1812515258789 -84 C106.1812515258789 -44.07795944922694, 106.1812515258789 -4.155918898453876, 106.1812515258789 84 M106.1812515258789 84 C57.04262022557376 84, 7.903988925268621 84, -106.1812515258789 84 M106.1812515258789 84 C57.25532918635319 84, 8.329406846827467 84, -106.1812515258789 84 M-106.1812515258789 84 C-106.1812515258789 28.106673201225398, -106.1812515258789 -27.786653597549204, -106.1812515258789 -84 M-106.1812515258789 84 C-106.1812515258789 35.768250104001034, -106.1812515258789 -12.463499791997933, -106.1812515258789 -84"></path></g><g transform="translate(0, -60)" class="annotation-group text"></g><g transform="translate(-38.13750076293945, -60)" class="label-group text"><g transform="translate(0,-12)" style="font-weight: bolder" class="label"><foreignObject height="24" width="76.2750015258789"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 131px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>SRAMBank</p></span></div></foreignObject></g></g><g transform="translate(-94.1812515258789, -12)" class="members-group text"><g transform="translate(0,-12)" style="" class="label"><foreignObject height="24" width="118.3125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 177px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+buffer: ndarray</p></span></div></foreignObject></g></g><g transform="translate(-94.1812515258789, 36)" class="methods-group text"><g transform="translate(0,-12)" style="" class="label"><foreignObject height="24" width="150.22500610351562"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 213px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+run_prim(primitive)</p></span></div></foreignObject></g><g transform="translate(0,12)" style="" class="label"><foreignObject height="24" width="142.77500915527344"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 203px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+calculate_energy()</p></span></div></foreignObject></g></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-106.1812515258789 -36 C-25.40580190005234 -36, 55.36964772577423 -36, 106.1812515258789 -36 M-106.1812515258789 -36 C-57.77617273280751 -36, -9.371093939736113 -36, 106.1812515258789 -36"></path></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-106.1812515258789 12 C-47.9771342988538 12, 10.22698292817131 12, 106.1812515258789 12 M-106.1812515258789 12 C-35.2423819941654 12, 35.69648753754811 12, 106.1812515258789 12"></path></g></g><g transform="translate(346.3562545776367, 1866)" id="classId-CIMPage-54" class="node default"><g class="basic label-container"><path style="" fill="#1f2020" stroke-width="0" stroke="none" d="M-102.25312805175781 -96 L102.25312805175781 -96 L102.25312805175781 96 L-102.25312805175781 96"></path><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-102.25312805175781 -96 C-45.06875571186049 -96, 12.115616628036832 -96, 102.25312805175781 -96 M-102.25312805175781 -96 C-21.452737562571755 -96, 59.3476529266143 -96, 102.25312805175781 -96 M102.25312805175781 -96 C102.25312805175781 -42.68799721705545, 102.25312805175781 10.624005565889107, 102.25312805175781 96 M102.25312805175781 -96 C102.25312805175781 -55.0286450176491, 102.25312805175781 -14.057290035298195, 102.25312805175781 96 M102.25312805175781 96 C35.4132039338665 96, -31.426720184024816 96, -102.25312805175781 96 M102.25312805175781 96 C35.703804770320914 96, -30.845518511115984 96, -102.25312805175781 96 M-102.25312805175781 96 C-102.25312805175781 49.87301468330207, -102.25312805175781 3.7460293666041338, -102.25312805175781 -96 M-102.25312805175781 96 C-102.25312805175781 28.145241206986057, -102.25312805175781 -39.709517586027886, -102.25312805175781 -96"></path></g><g transform="translate(0, -72)" class="annotation-group text"></g><g transform="translate(-30.28125, -72)" class="label-group text"><g transform="translate(0,-12)" style="font-weight: bolder" class="label"><foreignObject height="24" width="60.5625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 114px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>CIMPage</p></span></div></foreignObject></g></g><g transform="translate(-90.25312805175781, -24)" class="members-group text"><g transform="translate(0,-12)" style="" class="label"><foreignObject height="24" width="94.6875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 153px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+page_id: int</p></span></div></foreignObject></g><g transform="translate(0,12)" style="" class="label"><foreignObject height="24" width="118.3125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 177px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+buffer: ndarray</p></span></div></foreignObject></g></g><g transform="translate(-90.25312805175781, 48)" class="methods-group text"><g transform="translate(0,-12)" style="" class="label"><foreignObject height="24" width="150.22500610351562"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 213px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+run_prim(primitive)</p></span></div></foreignObject></g><g transform="translate(0,12)" style="" class="label"><foreignObject height="24" width="142.77500915527344"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 203px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+calculate_energy()</p></span></div></foreignObject></g></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-102.25312805175781 -48 C-53.38366198816443 -48, -4.514195924571041 -48, 102.25312805175781 -48 M-102.25312805175781 -48 C-31.492641471698093 -48, 39.26784510836163 -48, 102.25312805175781 -48"></path></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-102.25312805175781 24 C-30.336896456771086 24, 41.57933513821564 24, 102.25312805175781 24 M-102.25312805175781 24 C-26.954845419036957 24, 48.3434372136839 24, 102.25312805175781 24"></path></g></g><g transform="translate(102.65937805175781, 948)" id="classId-VPU-55" class="node default"><g class="basic label-container"><path style="" fill="#1f2020" stroke-width="0" stroke="none" d="M-94.65937805175781 -84 L94.65937805175781 -84 L94.65937805175781 84 L-94.65937805175781 84"></path><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-94.65937805175781 -84 C-19.164178777068514 -84, 56.331020497620784 -84, 94.65937805175781 -84 M-94.65937805175781 -84 C-53.61909260103327 -84, -12.578807150308734 -84, 94.65937805175781 -84 M94.65937805175781 -84 C94.65937805175781 -30.278402998238583, 94.65937805175781 23.443194003522834, 94.65937805175781 84 M94.65937805175781 -84 C94.65937805175781 -30.189626954076196, 94.65937805175781 23.620746091847607, 94.65937805175781 84 M94.65937805175781 84 C54.51449082806546 84, 14.369603604373111 84, -94.65937805175781 84 M94.65937805175781 84 C47.47915373614135 84, 0.29892942052488536 84, -94.65937805175781 84 M-94.65937805175781 84 C-94.65937805175781 17.04564174034546, -94.65937805175781 -49.90871651930908, -94.65937805175781 -84 M-94.65937805175781 84 C-94.65937805175781 33.85958548085163, -94.65937805175781 -16.280829038296744, -94.65937805175781 -84"></path></g><g transform="translate(0, -60)" class="annotation-group text"></g><g transform="translate(-15.09375, -60)" class="label-group text"><g transform="translate(0,-12)" style="font-weight: bolder" class="label"><foreignObject height="24" width="30.1875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 82px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>VPU</p></span></div></foreignObject></g></g><g transform="translate(-82.65937805175781, -12)" class="members-group text"><g transform="translate(0,-12)" style="" class="label"><foreignObject height="24" width="103.375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 164px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+vpe_num: int</p></span></div></foreignObject></g></g><g transform="translate(-82.65937805175781, 36)" class="methods-group text"><g transform="translate(0,-12)" style="" class="label"><foreignObject height="24" width="150.22500610351562"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 213px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+run_prim(primitive)</p></span></div></foreignObject></g><g transform="translate(0,12)" style="" class="label"><foreignObject height="24" width="142.77500915527344"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 203px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+calculate_energy()</p></span></div></foreignObject></g></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-94.65937805175781 -36 C-44.77471108082082 -36, 5.109955890116169 -36, 94.65937805175781 -36 M-94.65937805175781 -36 C-32.318291960153566 -36, 30.02279413145068 -36, 94.65937805175781 -36"></path></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-94.65937805175781 12 C-56.59475300926834 12, -18.530127966778863 12, 94.65937805175781 12 M-94.65937805175781 12 C-44.09429876919416 12, 6.4707805133694904 12, 94.65937805175781 12"></path></g></g><g transform="translate(356.3562545776367, 948)" id="classId-CIMCluster-56" class="node default"><g class="basic label-container"><path style="" fill="#1f2020" stroke-width="0" stroke="none" d="M-109.0374984741211 -96 L109.0374984741211 -96 L109.0374984741211 96 L-109.0374984741211 96"></path><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-109.0374984741211 -96 C-47.53359690774337 -96, 13.970304658634348 -96, 109.0374984741211 -96 M-109.0374984741211 -96 C-55.448296981328845 -96, -1.859095488536596 -96, 109.0374984741211 -96 M109.0374984741211 -96 C109.0374984741211 -31.009116714054528, 109.0374984741211 33.981766571890944, 109.0374984741211 96 M109.0374984741211 -96 C109.0374984741211 -49.10198517692188, 109.0374984741211 -2.203970353843758, 109.0374984741211 96 M109.0374984741211 96 C62.4482315662168 96, 15.858964658312502 96, -109.0374984741211 96 M109.0374984741211 96 C35.73787601291629 96, -37.56174644828852 96, -109.0374984741211 96 M-109.0374984741211 96 C-109.0374984741211 19.70402584792501, -109.0374984741211 -56.59194830414998, -109.0374984741211 -96 M-109.0374984741211 96 C-109.0374984741211 46.858149474460596, -109.0374984741211 -2.2837010510788076, -109.0374984741211 -96"></path></g><g transform="translate(0, -72)" class="annotation-group text"></g><g transform="translate(-39.70000076293945, -72)" class="label-group text"><g transform="translate(0,-12)" style="font-weight: bolder" class="label"><foreignObject height="24" width="79.4000015258789"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 131px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>CIMCluster</p></span></div></foreignObject></g></g><g transform="translate(-97.0374984741211, -24)" class="members-group text"><g transform="translate(0,-12)" style="" class="label"><foreignObject height="24" width="130.1125030517578"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 192px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+pages: CIMPage[]</p></span></div></foreignObject></g><g transform="translate(0,12)" style="" class="label"><foreignObject height="24" width="154.375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 215px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+macarray: MACArray</p></span></div></foreignObject></g></g><g transform="translate(-97.0374984741211, 48)" class="methods-group text"><g transform="translate(0,-12)" style="" class="label"><foreignObject height="24" width="150.22500610351562"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 213px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+run_prim(primitive)</p></span></div></foreignObject></g><g transform="translate(0,12)" style="" class="label"><foreignObject height="24" width="142.77500915527344"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 203px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+calculate_energy()</p></span></div></foreignObject></g></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-109.0374984741211 -48 C-36.394701106934875 -48, 36.248096260251344 -48, 109.0374984741211 -48 M-109.0374984741211 -48 C-32.32509749932075 -48, 44.3873034754796 -48, 109.0374984741211 -48"></path></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-109.0374984741211 24 C-37.498384808969206 24, 34.04072885618268 24, 109.0374984741211 24 M-109.0374984741211 24 C-29.32058914658886 24, 50.39632018094338 24, 109.0374984741211 24"></path></g></g><g transform="translate(629.1906280517578, 948)" id="classId-LoadStoreUnit-57" class="node default"><g class="basic label-container"><path style="" fill="#1f2020" stroke-width="0" stroke="none" d="M-113.796875 -75 L113.796875 -75 L113.796875 75 L-113.796875 75"></path><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-113.796875 -75 C-23.985089003406216 -75, 65.82669699318757 -75, 113.796875 -75 M-113.796875 -75 C-28.564205313343606 -75, 56.66846437331279 -75, 113.796875 -75 M113.796875 -75 C113.796875 -22.206769112027175, 113.796875 30.58646177594565, 113.796875 75 M113.796875 -75 C113.796875 -42.03644336572857, 113.796875 -9.072886731457146, 113.796875 75 M113.796875 75 C43.87550416207762 75, -26.045866675844763 75, -113.796875 75 M113.796875 75 C36.4360778393236 75, -40.924719321352796 75, -113.796875 75 M-113.796875 75 C-113.796875 33.706727610095164, -113.796875 -7.586544779809671, -113.796875 -75 M-113.796875 75 C-113.796875 30.027862818173276, -113.796875 -14.944274363653449, -113.796875 -75"></path></g><g transform="translate(0, -51)" class="annotation-group text"></g><g transform="translate(-53.368751525878906, -51)" class="label-group text"><g transform="translate(0,-12)" style="font-weight: bolder" class="label"><foreignObject height="24" width="106.73750305175781"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 158px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>LoadStoreUnit</p></span></div></foreignObject></g></g><g transform="translate(-101.796875, -3)" class="members-group text"></g><g transform="translate(-101.796875, 27)" class="methods-group text"><g transform="translate(0,-12)" style="" class="label"><foreignObject height="24" width="150.22500610351562"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 213px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+run_prim(primitive)</p></span></div></foreignObject></g><g transform="translate(0,12)" style="" class="label"><foreignObject height="24" width="142.77500915527344"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 203px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+calculate_energy()</p></span></div></foreignObject></g></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-113.796875 -27 C-61.15907049384645 -27, -8.5212659876929 -27, 113.796875 -27 M-113.796875 -27 C-47.17288481734873 -27, 19.45110536530254 -27, 113.796875 -27"></path></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-113.796875 -3 C-51.877181699957724 -3, 10.042511600084552 -3, 113.796875 -3 M-113.796875 -3 C-25.219588990643118 -3, 63.357697018713765 -3, 113.796875 -3"></path></g></g><g transform="translate(901.5781326293945, 948)" id="classId-ManageMod-58" class="node default"><g class="basic label-container"><path style="" fill="#1f2020" stroke-width="0" stroke="none" d="M-108.59062957763672 -75 L108.59062957763672 -75 L108.59062957763672 75 L-108.59062957763672 75"></path><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-108.59062957763672 -75 C-56.23276725008221 -75, -3.874904922527705 -75, 108.59062957763672 -75 M-108.59062957763672 -75 C-48.27077550010987 -75, 12.049078577416978 -75, 108.59062957763672 -75 M108.59062957763672 -75 C108.59062957763672 -39.06297572403499, 108.59062957763672 -3.1259514480699835, 108.59062957763672 75 M108.59062957763672 -75 C108.59062957763672 -15.654583671771292, 108.59062957763672 43.69083265645742, 108.59062957763672 75 M108.59062957763672 75 C38.53124522826201 75, -31.5281391211127 75, -108.59062957763672 75 M108.59062957763672 75 C65.11549077629931 75, 21.640351974961902 75, -108.59062957763672 75 M-108.59062957763672 75 C-108.59062957763672 38.188614785144544, -108.59062957763672 1.3772295702890887, -108.59062957763672 -75 M-108.59062957763672 75 C-108.59062957763672 34.73896402816712, -108.59062957763672 -5.522071943665765, -108.59062957763672 -75"></path></g><g transform="translate(0, -51)" class="annotation-group text"></g><g transform="translate(-42.95624923706055, -51)" class="label-group text"><g transform="translate(0,-12)" style="font-weight: bolder" class="label"><foreignObject height="24" width="85.9124984741211"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 141px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>ManageMod</p></span></div></foreignObject></g></g><g transform="translate(-96.59062957763672, -3)" class="members-group text"></g><g transform="translate(-96.59062957763672, 27)" class="methods-group text"><g transform="translate(0,-12)" style="" class="label"><foreignObject height="24" width="150.22500610351562"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 213px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+run_prim(primitive)</p></span></div></foreignObject></g><g transform="translate(0,12)" style="" class="label"><foreignObject height="24" width="142.77500915527344"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 203px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+calculate_energy()</p></span></div></foreignObject></g></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-108.59062957763672 -27 C-28.207870019345606 -27, 52.17488953894551 -27, 108.59062957763672 -27 M-108.59062957763672 -27 C-39.272749243072894 -27, 30.04513109149093 -27, 108.59062957763672 -27"></path></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-108.59062957763672 -3 C-51.955267227065406 -3, 4.680095123505907 -3, 108.59062957763672 -3 M-108.59062957763672 -3 C-56.64146415024844 -3, -4.6922987228601585 -3, 108.59062957763672 -3"></path></g></g><g transform="translate(1187.328140258789, 948)" id="classId-NoCRouter-59" class="node default"><g class="basic label-container"><path style="" fill="#1f2020" stroke-width="0" stroke="none" d="M-107.15937805175781 -96 L107.15937805175781 -96 L107.15937805175781 96 L-107.15937805175781 96"></path><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-107.15937805175781 -96 C-52.18558826896189 -96, 2.7882015138340392 -96, 107.15937805175781 -96 M-107.15937805175781 -96 C-50.09528648563521 -96, 6.9688050804873996 -96, 107.15937805175781 -96 M107.15937805175781 -96 C107.15937805175781 -46.397395136309946, 107.15937805175781 3.205209727380108, 107.15937805175781 96 M107.15937805175781 -96 C107.15937805175781 -19.39198917610929, 107.15937805175781 57.21602164778142, 107.15937805175781 96 M107.15937805175781 96 C53.14786017711721 96, -0.8636576975233936 96, -107.15937805175781 96 M107.15937805175781 96 C58.997615684994194 96, 10.835853318230576 96, -107.15937805175781 96 M-107.15937805175781 96 C-107.15937805175781 36.27434988755539, -107.15937805175781 -23.451300224889224, -107.15937805175781 -96 M-107.15937805175781 96 C-107.15937805175781 44.98907956558552, -107.15937805175781 -6.02184086882896, -107.15937805175781 -96"></path></g><g transform="translate(0, -72)" class="annotation-group text"></g><g transform="translate(-40.09375, -72)" class="label-group text"><g transform="translate(0,-12)" style="font-weight: bolder" class="label"><foreignObject height="24" width="80.1875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 133px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>NoCRouter</p></span></div></foreignObject></g></g><g transform="translate(-95.15937805175781, -24)" class="members-group text"><g transform="translate(0,-12)" style="" class="label"><foreignObject height="24" width="108.4625015258789"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 167px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+port_type: str</p></span></div></foreignObject></g><g transform="translate(0,12)" style="" class="label"><foreignObject height="24" width="97.8375015258789"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 160px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+noc: Mesh2D</p></span></div></foreignObject></g></g><g transform="translate(-95.15937805175781, 48)" class="methods-group text"><g transform="translate(0,-12)" style="" class="label"><foreignObject height="24" width="150.22500610351562"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 213px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+run_prim(primitive)</p></span></div></foreignObject></g><g transform="translate(0,12)" style="" class="label"><foreignObject height="24" width="142.77500915527344"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 203px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+calculate_energy()</p></span></div></foreignObject></g></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-107.15937805175781 -48 C-50.48479925548665 -48, 6.189779540784514 -48, 107.15937805175781 -48 M-107.15937805175781 -48 C-42.183661882598685 -48, 22.792054286560443 -48, 107.15937805175781 -48"></path></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-107.15937805175781 24 C-50.25884138532619 24, 6.641695281105427 24, 107.15937805175781 24 M-107.15937805175781 24 C-45.00217317867587 24, 17.155031694406077 24, 107.15937805175781 24"></path></g></g><g transform="translate(1116.2484512329102, 1274)" id="classId-Mesh2D-60" class="node default"><g class="basic label-container"><path style="" fill="#1f2020" stroke-width="0" stroke="none" d="M-140.25625610351562 -120 L140.25625610351562 -120 L140.25625610351562 120 L-140.25625610351562 120"></path><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-140.25625610351562 -120 C-46.00058903714587 -120, 48.25507802922388 -120, 140.25625610351562 -120 M-140.25625610351562 -120 C-56.31224970474452 -120, 27.631756694026592 -120, 140.25625610351562 -120 M140.25625610351562 -120 C140.25625610351562 -56.02658275800766, 140.25625610351562 7.946834483984674, 140.25625610351562 120 M140.25625610351562 -120 C140.25625610351562 -45.601973224342316, 140.25625610351562 28.796053551315367, 140.25625610351562 120 M140.25625610351562 120 C50.030133113945894 120, -40.19598987562384 120, -140.25625610351562 120 M140.25625610351562 120 C38.38550709342272 120, -63.48524191667019 120, -140.25625610351562 120 M-140.25625610351562 120 C-140.25625610351562 30.75845240537967, -140.25625610351562 -58.48309518924066, -140.25625610351562 -120 M-140.25625610351562 120 C-140.25625610351562 29.19549166380527, -140.25625610351562 -61.60901667238946, -140.25625610351562 -120"></path></g><g transform="translate(0, -96)" class="annotation-group text"></g><g transform="translate(-28.575000762939453, -96)" class="label-group text"><g transform="translate(0,-12)" style="font-weight: bolder" class="label"><foreignObject height="24" width="57.150001525878906"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 109px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>Mesh2D</p></span></div></foreignObject></g></g><g transform="translate(-128.25625610351562, -48)" class="members-group text"><g transform="translate(0,-12)" style="" class="label"><foreignObject height="24" width="65.45000457763672"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 122px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+row: int</p></span></div></foreignObject></g><g transform="translate(0,12)" style="" class="label"><foreignObject height="24" width="59.962501525878906"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 116px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+col: int</p></span></div></foreignObject></g><g transform="translate(0,36)" style="" class="label"><foreignObject height="24" width="173.97500610351562"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 240px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+nodes: Node2DMesh[][]</p></span></div></foreignObject></g><g transform="translate(0,60)" style="" class="label"><foreignObject height="24" width="183.4499969482422"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 247px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+links_x: Link2DMeshX[][]</p></span></div></foreignObject></g><g transform="translate(0,84)" style="" class="label"><foreignObject height="24" width="183.5500030517578"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 246px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+links_y: Link2DMeshY[][]</p></span></div></foreignObject></g></g><g transform="translate(-128.25625610351562, 96)" class="methods-group text"><g transform="translate(0,-12)" style="" class="label"><foreignObject height="24" width="227.9375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 293px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+routing_request(master, slave)</p></span></div></foreignObject></g></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-140.25625610351562 -72 C-61.337251875509594 -72, 17.581752352496437 -72, 140.25625610351562 -72 M-140.25625610351562 -72 C-51.91149038456753 -72, 36.43327533438057 -72, 140.25625610351562 -72"></path></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-140.25625610351562 72 C-48.05575715076593 72, 44.14474180198377 72, 140.25625610351562 72 M-140.25625610351562 72 C-60.26501941469442 72, 19.726217274126782 72, 140.25625610351562 72"></path></g></g><g transform="translate(1116.2484512329102, 1600)" id="classId-Node2DMesh-61" class="node default"><g class="basic label-container"><path style="" fill="#1f2020" stroke-width="0" stroke="none" d="M-104.7437515258789 -96 L104.7437515258789 -96 L104.7437515258789 96 L-104.7437515258789 96"></path><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-104.7437515258789 -96 C-40.45024843594477 -96, 23.843254653989362 -96, 104.7437515258789 -96 M-104.7437515258789 -96 C-54.12803220964441 -96, -3.512312893409913 -96, 104.7437515258789 -96 M104.7437515258789 -96 C104.7437515258789 -54.663623373342425, 104.7437515258789 -13.32724674668485, 104.7437515258789 96 M104.7437515258789 -96 C104.7437515258789 -34.74293828739597, 104.7437515258789 26.514123425208055, 104.7437515258789 96 M104.7437515258789 96 C27.912353552611506 96, -48.919044420655894 96, -104.7437515258789 96 M104.7437515258789 96 C26.888819447982257 96, -50.96611262991439 96, -104.7437515258789 96 M-104.7437515258789 96 C-104.7437515258789 28.977456045108042, -104.7437515258789 -38.045087909783916, -104.7437515258789 -96 M-104.7437515258789 96 C-104.7437515258789 24.437933491038038, -104.7437515258789 -47.124133017923924, -104.7437515258789 -96"></path></g><g transform="translate(0, -72)" class="annotation-group text"></g><g transform="translate(-47.6875, -72)" class="label-group text"><g transform="translate(0,-12)" style="font-weight: bolder" class="label"><foreignObject height="24" width="95.375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 149px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>Node2DMesh</p></span></div></foreignObject></g></g><g transform="translate(-92.7437515258789, -24)" class="members-group text"><g transform="translate(0,-12)" style="" class="label"><foreignObject height="24" width="46.75"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 102px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+x: int</p></span></div></foreignObject></g><g transform="translate(0,12)" style="" class="label"><foreignObject height="24" width="46.625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 102px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+y: int</p></span></div></foreignObject></g><g transform="translate(0,36)" style="" class="label"><foreignObject height="24" width="137.8000030517578"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+master_port: bool</p></span></div></foreignObject></g><g transform="translate(0,60)" style="" class="label"><foreignObject height="24" width="124.51250457763672"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 186px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+slave_port: bool</p></span></div></foreignObject></g></g><g transform="translate(-92.7437515258789, 96)" class="methods-group text"></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-104.7437515258789 -48 C-43.650997853073534 -48, 17.44175581973184 -48, 104.7437515258789 -48 M-104.7437515258789 -48 C-23.54252902555878 -48, 57.65869347476135 -48, 104.7437515258789 -48"></path></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-104.7437515258789 72 C-46.27632593446413 72, 12.191099656950641 72, 104.7437515258789 72 M-104.7437515258789 72 C-61.63931171524381 72, -18.534871904608707 72, 104.7437515258789 72"></path></g></g><g transform="translate(1494.954704284668, 948)" id="classId-Primitive-62" class="node default"><g class="basic label-container"><path style="" fill="#1f2020" stroke-width="0" stroke="none" d="M-113.91874694824219 -120 L113.91874694824219 -120 L113.91874694824219 120 L-113.91874694824219 120"></path><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-113.91874694824219 -120 C-48.50753166006618 -120, 16.90368362810983 -120, 113.91874694824219 -120 M-113.91874694824219 -120 C-59.52147307662025 -120, -5.12419920499832 -120, 113.91874694824219 -120 M113.91874694824219 -120 C113.91874694824219 -70.28504210840171, 113.91874694824219 -20.57008421680341, 113.91874694824219 120 M113.91874694824219 -120 C113.91874694824219 -63.45837184263227, 113.91874694824219 -6.916743685264535, 113.91874694824219 120 M113.91874694824219 120 C49.798608159520924 120, -14.321530629200339 120, -113.91874694824219 120 M113.91874694824219 120 C34.90132030861848 120, -44.11610633100523 120, -113.91874694824219 120 M-113.91874694824219 120 C-113.91874694824219 50.181302508349546, -113.91874694824219 -19.637394983300908, -113.91874694824219 -120 M-113.91874694824219 120 C-113.91874694824219 35.51783088975283, -113.91874694824219 -48.96433822049434, -113.91874694824219 -120"></path></g><g transform="translate(0, -96)" class="annotation-group text"></g><g transform="translate(-34.13750076293945, -96)" class="label-group text"><g transform="translate(0,-12)" style="font-weight: bolder" class="label"><foreignObject height="24" width="68.2750015258789"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 117px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>Primitive</p></span></div></foreignObject></g></g><g transform="translate(-101.91874694824219, -48)" class="members-group text"><g transform="translate(0,-12)" style="" class="label"><foreignObject height="24" width="123.82500457763672"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 186px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+type: PrimName</p></span></div></foreignObject></g><g transform="translate(0,12)" style="" class="label"><foreignObject height="24" width="167.72500610351562"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 232px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+tensor_in1: TensorInfo</p></span></div></foreignObject></g><g transform="translate(0,36)" style="" class="label"><foreignObject height="24" width="167.72500610351562"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 232px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+tensor_in2: TensorInfo</p></span></div></foreignObject></g><g transform="translate(0,60)" style="" class="label"><foreignObject height="24" width="169.6999969482422"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 235px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+tensor_out: TensorInfo</p></span></div></foreignObject></g><g transform="translate(0,84)" style="" class="label"><foreignObject height="24" width="79.23750305175781"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 138px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+group: int</p></span></div></foreignObject></g><g transform="translate(0,108)" style="" class="label"><foreignObject height="24" width="74.9749984741211"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 134px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+mask: int</p></span></div></foreignObject></g></g><g transform="translate(-101.91874694824219, 120)" class="methods-group text"></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-113.91874694824219 -72 C-57.31262440163722 -72, -0.7065018550322577 -72, 113.91874694824219 -72 M-113.91874694824219 -72 C-64.06736364198763 -72, -14.215980335733065 -72, 113.91874694824219 -72"></path></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-113.91874694824219 96 C-58.94799764316884 96, -3.9772483380954924 96, 113.91874694824219 96 M-113.91874694824219 96 C-56.05389595689738 96, 1.8109550344474314 96, 113.91874694824219 96"></path></g></g><g transform="translate(1494.954704284668, 1274)" id="classId-TensorInfo-63" class="node default"><g class="basic label-container"><path style="" fill="#1f2020" stroke-width="0" stroke="none" d="M-112.5718765258789 -132 L112.5718765258789 -132 L112.5718765258789 132 L-112.5718765258789 132"></path><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-112.5718765258789 -132 C-29.27832536419301 -132, 54.01522579749289 -132, 112.5718765258789 -132 M-112.5718765258789 -132 C-23.49011801513379 -132, 65.59164049561133 -132, 112.5718765258789 -132 M112.5718765258789 -132 C112.5718765258789 -39.61626269929572, 112.5718765258789 52.767474601408566, 112.5718765258789 132 M112.5718765258789 -132 C112.5718765258789 -73.93988139431386, 112.5718765258789 -15.879762788627716, 112.5718765258789 132 M112.5718765258789 132 C44.16376475902807 132, -24.244347007822768 132, -112.5718765258789 132 M112.5718765258789 132 C43.02894843924474 132, -26.51397964738942 132, -112.5718765258789 132 M-112.5718765258789 132 C-112.5718765258789 52.84293507153191, -112.5718765258789 -26.314129856936177, -112.5718765258789 -132 M-112.5718765258789 132 C-112.5718765258789 29.490028130444173, -112.5718765258789 -73.01994373911165, -112.5718765258789 -132"></path></g><g transform="translate(0, -108)" class="annotation-group text"></g><g transform="translate(-39.15625, -108)" class="label-group text"><g transform="translate(0,-12)" style="font-weight: bolder" class="label"><foreignObject height="24" width="78.3125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 130px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>TensorInfo</p></span></div></foreignObject></g></g><g transform="translate(-100.5718765258789, -60)" class="members-group text"><g transform="translate(0,-12)" style="" class="label"><foreignObject height="24" width="111.5250015258789"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 170px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+byte_base: int</p></span></div></foreignObject></g><g transform="translate(0,12)" style="" class="label"><foreignObject height="24" width="138.0500030517578"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 196px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+byte_stride: tuple</p></span></div></foreignObject></g><g transform="translate(0,36)" style="" class="label"><foreignObject height="24" width="83.9000015258789"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 140px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+size: tuple</p></span></div></foreignObject></g><g transform="translate(0,60)" style="" class="label"><foreignObject height="24" width="70"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 127px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+type: str</p></span></div></foreignObject></g><g transform="translate(0,84)" style="" class="label"><foreignObject height="24" width="79.2125015258789"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 137px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+width: int</p></span></div></foreignObject></g><g transform="translate(0,108)" style="" class="label"><foreignObject height="24" width="91.5250015258789"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 149px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+layout: dict</p></span></div></foreignObject></g><g transform="translate(0,132)" style="" class="label"><foreignObject height="24" width="161.9875030517578"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 230px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="" class="nodeLabel markdown-node-label"><p>+memory: MemoryMod</p></span></div></foreignObject></g></g><g transform="translate(-100.5718765258789, 132)" class="methods-group text"></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-112.5718765258789 -84 C-33.6686481451804 -84, 45.23458023551811 -84, 112.5718765258789 -84 M-112.5718765258789 -84 C-59.587559220694196 -84, -6.603241915509486 -84, 112.5718765258789 -84"></path></g><g style="" class="divider"><path style="" fill="none" stroke-width="1.3" stroke="#ccc" d="M-112.5718765258789 108 C-61.73952784959739 108, -10.907179173315868 108, 112.5718765258789 108 M-112.5718765258789 108 C-31.92775692952921 108, 48.71636266682049 108, 112.5718765258789 108"></path></g></g></g></g></g></svg>