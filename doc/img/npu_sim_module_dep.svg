<svg aria-roledescription="flowchart-v2" role="graphics-document document" viewBox="0 0 2845.175048828125 742" style="max-width: 2845.175048828125px;" class="flowchart" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns="http://www.w3.org/2000/svg" width="100%" id="mermaid-9b1cc3a2-278f-49f9-a45b-9fb34bdd50bb"><style>#mermaid-9b1cc3a2-278f-49f9-a45b-9fb34bdd50bb{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;fill:#ccc;}#mermaid-9b1cc3a2-278f-49f9-a45b-9fb34bdd50bb .error-icon{fill:#a44141;}#mermaid-9b1cc3a2-278f-49f9-a45b-9fb34bdd50bb .error-text{fill:#ddd;stroke:#ddd;}#mermaid-9b1cc3a2-278f-49f9-a45b-9fb34bdd50bb .edge-thickness-normal{stroke-width:1px;}#mermaid-9b1cc3a2-278f-49f9-a45b-9fb34bdd50bb .edge-thickness-thick{stroke-width:3.5px;}#mermaid-9b1cc3a2-278f-49f9-a45b-9fb34bdd50bb .edge-pattern-solid{stroke-dasharray:0;}#mermaid-9b1cc3a2-278f-49f9-a45b-9fb34bdd50bb .edge-thickness-invisible{stroke-width:0;fill:none;}#mermaid-9b1cc3a2-278f-49f9-a45b-9fb34bdd50bb .edge-pattern-dashed{stroke-dasharray:3;}#mermaid-9b1cc3a2-278f-49f9-a45b-9fb34bdd50bb .edge-pattern-dotted{stroke-dasharray:2;}#mermaid-9b1cc3a2-278f-49f9-a45b-9fb34bdd50bb .marker{fill:lightgrey;stroke:lightgrey;}#mermaid-9b1cc3a2-278f-49f9-a45b-9fb34bdd50bb .marker.cross{stroke:lightgrey;}#mermaid-9b1cc3a2-278f-49f9-a45b-9fb34bdd50bb svg{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;}#mermaid-9b1cc3a2-278f-49f9-a45b-9fb34bdd50bb p{margin:0;}#mermaid-9b1cc3a2-278f-49f9-a45b-9fb34bdd50bb .label{font-family:"trebuchet ms",verdana,arial,sans-serif;color:#ccc;}#mermaid-9b1cc3a2-278f-49f9-a45b-9fb34bdd50bb .cluster-label text{fill:#F9FFFE;}#mermaid-9b1cc3a2-278f-49f9-a45b-9fb34bdd50bb .cluster-label span{color:#F9FFFE;}#mermaid-9b1cc3a2-278f-49f9-a45b-9fb34bdd50bb .cluster-label span p{background-color:transparent;}#mermaid-9b1cc3a2-278f-49f9-a45b-9fb34bdd50bb .label text,#mermaid-9b1cc3a2-278f-49f9-a45b-9fb34bdd50bb span{fill:#ccc;color:#ccc;}#mermaid-9b1cc3a2-278f-49f9-a45b-9fb34bdd50bb .node rect,#mermaid-9b1cc3a2-278f-49f9-a45b-9fb34bdd50bb .node circle,#mermaid-9b1cc3a2-278f-49f9-a45b-9fb34bdd50bb .node ellipse,#mermaid-9b1cc3a2-278f-49f9-a45b-9fb34bdd50bb .node polygon,#mermaid-9b1cc3a2-278f-49f9-a45b-9fb34bdd50bb .node path{fill:#1f2020;stroke:#ccc;stroke-width:1px;}#mermaid-9b1cc3a2-278f-49f9-a45b-9fb34bdd50bb .rough-node .label text,#mermaid-9b1cc3a2-278f-49f9-a45b-9fb34bdd50bb .node .label text,#mermaid-9b1cc3a2-278f-49f9-a45b-9fb34bdd50bb .image-shape .label,#mermaid-9b1cc3a2-278f-49f9-a45b-9fb34bdd50bb .icon-shape .label{text-anchor:middle;}#mermaid-9b1cc3a2-278f-49f9-a45b-9fb34bdd50bb .node .katex path{fill:#000;stroke:#000;stroke-width:1px;}#mermaid-9b1cc3a2-278f-49f9-a45b-9fb34bdd50bb .rough-node .label,#mermaid-9b1cc3a2-278f-49f9-a45b-9fb34bdd50bb .node .label,#mermaid-9b1cc3a2-278f-49f9-a45b-9fb34bdd50bb .image-shape .label,#mermaid-9b1cc3a2-278f-49f9-a45b-9fb34bdd50bb .icon-shape .label{text-align:center;}#mermaid-9b1cc3a2-278f-49f9-a45b-9fb34bdd50bb .node.clickable{cursor:pointer;}#mermaid-9b1cc3a2-278f-49f9-a45b-9fb34bdd50bb .root .anchor path{fill:lightgrey!important;stroke-width:0;stroke:lightgrey;}#mermaid-9b1cc3a2-278f-49f9-a45b-9fb34bdd50bb .arrowheadPath{fill:lightgrey;}#mermaid-9b1cc3a2-278f-49f9-a45b-9fb34bdd50bb .edgePath .path{stroke:lightgrey;stroke-width:2.0px;}#mermaid-9b1cc3a2-278f-49f9-a45b-9fb34bdd50bb .flowchart-link{stroke:lightgrey;fill:none;}#mermaid-9b1cc3a2-278f-49f9-a45b-9fb34bdd50bb .edgeLabel{background-color:hsl(0, 0%, 34.4117647059%);text-align:center;}#mermaid-9b1cc3a2-278f-49f9-a45b-9fb34bdd50bb .edgeLabel p{background-color:hsl(0, 0%, 34.4117647059%);}#mermaid-9b1cc3a2-278f-49f9-a45b-9fb34bdd50bb .edgeLabel rect{opacity:0.5;background-color:hsl(0, 0%, 34.4117647059%);fill:hsl(0, 0%, 34.4117647059%);}#mermaid-9b1cc3a2-278f-49f9-a45b-9fb34bdd50bb .labelBkg{background-color:rgba(87.75, 87.75, 87.75, 0.5);}#mermaid-9b1cc3a2-278f-49f9-a45b-9fb34bdd50bb .cluster rect{fill:hsl(180, 1.5873015873%, 28.3529411765%);stroke:rgba(255, 255, 255, 0.25);stroke-width:1px;}#mermaid-9b1cc3a2-278f-49f9-a45b-9fb34bdd50bb .cluster text{fill:#F9FFFE;}#mermaid-9b1cc3a2-278f-49f9-a45b-9fb34bdd50bb .cluster span{color:#F9FFFE;}#mermaid-9b1cc3a2-278f-49f9-a45b-9fb34bdd50bb div.mermaidTooltip{position:absolute;text-align:center;max-width:200px;padding:2px;font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:12px;background:hsl(20, 1.5873015873%, 12.3529411765%);border:1px solid rgba(255, 255, 255, 0.25);border-radius:2px;pointer-events:none;z-index:100;}#mermaid-9b1cc3a2-278f-49f9-a45b-9fb34bdd50bb .flowchartTitleText{text-anchor:middle;font-size:18px;fill:#ccc;}#mermaid-9b1cc3a2-278f-49f9-a45b-9fb34bdd50bb rect.text{fill:none;stroke-width:0;}#mermaid-9b1cc3a2-278f-49f9-a45b-9fb34bdd50bb .icon-shape,#mermaid-9b1cc3a2-278f-49f9-a45b-9fb34bdd50bb .image-shape{background-color:hsl(0, 0%, 34.4117647059%);text-align:center;}#mermaid-9b1cc3a2-278f-49f9-a45b-9fb34bdd50bb .icon-shape p,#mermaid-9b1cc3a2-278f-49f9-a45b-9fb34bdd50bb .image-shape p{background-color:hsl(0, 0%, 34.4117647059%);padding:2px;}#mermaid-9b1cc3a2-278f-49f9-a45b-9fb34bdd50bb .icon-shape rect,#mermaid-9b1cc3a2-278f-49f9-a45b-9fb34bdd50bb .image-shape rect{opacity:0.5;background-color:hsl(0, 0%, 34.4117647059%);fill:hsl(0, 0%, 34.4117647059%);}#mermaid-9b1cc3a2-278f-49f9-a45b-9fb34bdd50bb :root{--mermaid-font-family:"trebuchet ms",verdana,arial,sans-serif;}#mermaid-9b1cc3a2-278f-49f9-a45b-9fb34bdd50bb .entryPoint&gt;*{fill:#e1f5fe!important;stroke:#01579b!important;}#mermaid-9b1cc3a2-278f-49f9-a45b-9fb34bdd50bb .entryPoint span{fill:#e1f5fe!important;stroke:#01579b!important;}#mermaid-9b1cc3a2-278f-49f9-a45b-9fb34bdd50bb .coreModule&gt;*{fill:#fff3e0!important;stroke:#e65100!important;}#mermaid-9b1cc3a2-278f-49f9-a45b-9fb34bdd50bb .coreModule span{fill:#fff3e0!important;stroke:#e65100!important;}#mermaid-9b1cc3a2-278f-49f9-a45b-9fb34bdd50bb .hwModule&gt;*{fill:#f3e5f5!important;stroke:#4a148c!important;}#mermaid-9b1cc3a2-278f-49f9-a45b-9fb34bdd50bb .hwModule span{fill:#f3e5f5!important;stroke:#4a148c!important;}#mermaid-9b1cc3a2-278f-49f9-a45b-9fb34bdd50bb .external&gt;*{fill:#e8f5e8!important;stroke:#1b5e20!important;}#mermaid-9b1cc3a2-278f-49f9-a45b-9fb34bdd50bb .external span{fill:#e8f5e8!important;stroke:#1b5e20!important;}#mermaid-9b1cc3a2-278f-49f9-a45b-9fb34bdd50bb .config&gt;*{fill:#fce4ec!important;stroke:#880e4f!important;}#mermaid-9b1cc3a2-278f-49f9-a45b-9fb34bdd50bb .config span{fill:#fce4ec!important;stroke:#880e4f!important;}</style><g><marker orient="auto" markerHeight="8" markerWidth="8" markerUnits="userSpaceOnUse" refY="5" refX="5" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-9b1cc3a2-278f-49f9-a45b-9fb34bdd50bb_flowchart-v2-pointEnd"><path style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 0 L 10 5 L 0 10 z"></path></marker><marker orient="auto" markerHeight="8" markerWidth="8" markerUnits="userSpaceOnUse" refY="5" refX="4.5" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-9b1cc3a2-278f-49f9-a45b-9fb34bdd50bb_flowchart-v2-pointStart"><path style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 5 L 10 10 L 10 0 z"></path></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5" refX="11" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-9b1cc3a2-278f-49f9-a45b-9fb34bdd50bb_flowchart-v2-circleEnd"><circle style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"></circle></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5" refX="-1" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-9b1cc3a2-278f-49f9-a45b-9fb34bdd50bb_flowchart-v2-circleStart"><circle style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"></circle></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5.2" refX="12" viewBox="0 0 11 11" class="marker cross flowchart-v2" id="mermaid-9b1cc3a2-278f-49f9-a45b-9fb34bdd50bb_flowchart-v2-crossEnd"><path style="stroke-width: 2; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 l 9,9 M 10,1 l -9,9"></path></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5.2" refX="-1" viewBox="0 0 11 11" class="marker cross flowchart-v2" id="mermaid-9b1cc3a2-278f-49f9-a45b-9fb34bdd50bb_flowchart-v2-crossStart"><path style="stroke-width: 2; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 l 9,9 M 10,1 l -9,9"></path></marker><g class="root"><g class="clusters"></g><g class="edgePaths"><path marker-end="url(#mermaid-9b1cc3a2-278f-49f9-a45b-9fb34bdd50bb_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Demo_SIMTop_0" d="M1786.056,40.075L1665.097,47.896C1544.139,55.717,1302.221,71.358,1181.262,82.679C1060.303,94,1060.303,101,1060.303,104.5L1060.303,108"></path><path marker-end="url(#mermaid-9b1cc3a2-278f-49f9-a45b-9fb34bdd50bb_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Demo_IPC_1" d="M1864.55,62L1864.55,66.167C1864.55,70.333,1864.55,78.667,1864.55,86.333C1864.55,94,1864.55,101,1864.55,104.5L1864.55,108"></path><path marker-end="url(#mermaid-9b1cc3a2-278f-49f9-a45b-9fb34bdd50bb_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Demo_Decoder_2" d="M1943.044,40.819L2046.868,48.516C2150.692,56.213,2358.34,71.606,2462.164,82.803C2565.988,94,2565.988,101,2565.988,104.5L2565.988,108"></path><path marker-end="url(#mermaid-9b1cc3a2-278f-49f9-a45b-9fb34bdd50bb_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_SIMTop_NPUCore_3" d="M994.809,145.696L920.958,153.247C847.106,160.797,699.403,175.899,625.552,186.949C551.7,198,551.7,205,551.7,208.5L551.7,212"></path><path marker-end="url(#mermaid-9b1cc3a2-278f-49f9-a45b-9fb34bdd50bb_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_SIMTop_NoC_4" d="M994.809,165.498L984.304,169.748C973.798,173.998,952.786,182.499,942.281,195.416C931.775,208.333,931.775,225.667,931.775,243C931.775,260.333,931.775,277.667,931.775,295C931.775,312.333,931.775,329.667,931.775,349C931.775,368.333,931.775,389.667,931.775,411C931.775,432.333,931.775,453.667,931.775,473C931.775,492.333,931.775,509.667,931.775,527C931.775,544.333,931.775,561.667,931.775,581C931.775,600.333,931.775,621.667,833.056,642.026C734.336,662.386,536.898,681.772,438.178,691.464L339.459,701.157"></path><path marker-end="url(#mermaid-9b1cc3a2-278f-49f9-a45b-9fb34bdd50bb_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_SIMTop_SIMTemplate_5" d="M1003.952,166L995.256,170.167C986.56,174.333,969.167,182.667,960.471,195.5C951.775,208.333,951.775,225.667,951.775,243C951.775,260.333,951.775,277.667,951.775,295C951.775,312.333,951.775,329.667,951.775,349C951.775,368.333,951.775,389.667,882.359,409.1C812.944,428.533,674.112,446.067,604.697,454.833L535.281,463.6"></path><path marker-end="url(#mermaid-9b1cc3a2-278f-49f9-a45b-9fb34bdd50bb_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_SIMTop_GlobalSettings_6" d="M1014.337,166L1007.243,170.167C1000.149,174.333,985.962,182.667,978.869,195.5C971.775,208.333,971.775,225.667,971.775,243C971.775,260.333,971.775,277.667,971.775,295C971.775,312.333,971.775,329.667,971.775,349C971.775,368.333,971.775,389.667,971.775,411C971.775,432.333,971.775,453.667,971.775,473C971.775,492.333,971.775,509.667,867.396,525.839C763.017,542.011,554.26,557.023,449.881,564.528L345.502,572.034"></path><path marker-end="url(#mermaid-9b1cc3a2-278f-49f9-a45b-9fb34bdd50bb_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_SIMTop_GoldenModel_7" d="M1078.069,166L1080.811,170.167C1083.552,174.333,1089.035,182.667,1091.777,190.333C1094.519,198,1094.519,205,1094.519,208.5L1094.519,212"></path><path marker-end="url(#mermaid-9b1cc3a2-278f-49f9-a45b-9fb34bdd50bb_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_SIMTop_Backend_8" d="M1125.797,160.698L1141.041,165.748C1156.285,170.799,1186.774,180.899,1202.018,194.616C1217.263,208.333,1217.263,225.667,1217.263,243C1217.263,260.333,1217.263,277.667,1217.263,295C1217.263,312.333,1217.263,329.667,1217.263,349C1217.263,368.333,1217.263,389.667,1217.263,411C1217.263,432.333,1217.263,453.667,1217.263,473C1217.263,492.333,1217.263,509.667,1117.186,525.611C1017.109,541.555,816.955,556.109,716.879,563.386L616.802,570.664"></path><path marker-end="url(#mermaid-9b1cc3a2-278f-49f9-a45b-9fb34bdd50bb_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_SIMTop_Visualize_9" d="M1125.797,150.91L1162.541,157.591C1199.285,164.273,1272.774,177.637,1309.518,187.818C1346.263,198,1346.263,205,1346.263,208.5L1346.263,212"></path><path marker-end="url(#mermaid-9b1cc3a2-278f-49f9-a45b-9fb34bdd50bb_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_SIMTop_Utility_10" d="M1125.797,147.207L1184.041,154.506C1242.285,161.805,1358.774,176.402,1417.018,192.368C1475.263,208.333,1475.263,225.667,1475.263,243C1475.263,260.333,1475.263,277.667,1475.263,295C1475.263,312.333,1475.263,329.667,1475.263,349C1475.263,368.333,1475.263,389.667,1475.263,411C1475.263,432.333,1475.263,453.667,1475.263,473C1475.263,492.333,1475.263,509.667,1361.44,526.228C1247.618,542.79,1019.973,558.58,906.15,566.475L792.328,574.37"></path><path marker-end="url(#mermaid-9b1cc3a2-278f-49f9-a45b-9fb34bdd50bb_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_NPUCore_Modules_11" d="M479.05,250.977L412.225,258.314C345.4,265.651,211.75,280.326,144.925,291.163C78.1,302,78.1,309,78.1,312.5L78.1,316"></path><path marker-end="url(#mermaid-9b1cc3a2-278f-49f9-a45b-9fb34bdd50bb_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_NPUCore_SIMTemplate_12" d="M496.305,270L487.756,274.167C479.207,278.333,462.11,286.667,453.561,299.5C445.013,312.333,445.013,329.667,445.013,349C445.013,368.333,445.013,389.667,445.013,405.833C445.013,422,445.013,433,445.013,438.5L445.013,444"></path><path marker-end="url(#mermaid-9b1cc3a2-278f-49f9-a45b-9fb34bdd50bb_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_NPUCore_GlobalSettings_13" d="M612.33,270L621.686,274.167C631.043,278.333,649.756,286.667,659.112,299.5C668.469,312.333,668.469,329.667,668.469,349C668.469,368.333,668.469,389.667,668.469,411C668.469,432.333,668.469,453.667,668.469,473C668.469,492.333,668.469,509.667,614.638,525.001C560.807,540.335,453.144,553.669,399.313,560.336L345.482,567.004"></path><path marker-end="url(#mermaid-9b1cc3a2-278f-49f9-a45b-9fb34bdd50bb_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_NPUCore_Backend_14" d="M622.715,270L633.674,274.167C644.633,278.333,666.551,286.667,677.51,299.5C688.469,312.333,688.469,329.667,688.469,349C688.469,368.333,688.469,389.667,688.469,411C688.469,432.333,688.469,453.667,688.469,473C688.469,492.333,688.469,509.667,674.182,522.321C659.896,534.975,631.324,542.95,617.037,546.937L602.751,550.925"></path><path marker-end="url(#mermaid-9b1cc3a2-278f-49f9-a45b-9fb34bdd50bb_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Modules_SIMTemplate_15" d="M57.473,374L52.762,380.167C48.051,386.333,38.628,398.667,88.176,413.185C137.724,427.703,246.241,444.406,300.5,452.757L354.759,461.108"></path><path marker-end="url(#mermaid-9b1cc3a2-278f-49f9-a45b-9fb34bdd50bb_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Modules_GlobalSettings_16" d="M65.91,374L63.126,380.167C60.342,386.333,54.774,398.667,51.99,415.5C49.206,432.333,49.206,453.667,49.206,473C49.206,492.333,49.206,509.667,66.316,522.795C83.426,535.923,117.647,544.846,134.757,549.308L151.867,553.77"></path><path marker-end="url(#mermaid-9b1cc3a2-278f-49f9-a45b-9fb34bdd50bb_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_NoC_GlobalSettings_17" d="M315.998,680L324.231,673.833C332.464,667.667,348.929,655.333,346.495,643.32C344.061,631.308,322.728,619.615,312.061,613.769L301.395,607.923"></path><path marker-end="url(#mermaid-9b1cc3a2-278f-49f9-a45b-9fb34bdd50bb_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_SIMTemplate_GlobalSettings_18" d="M358.712,493.061L331.683,498.717C304.654,504.374,250.596,515.687,227.269,525.039C203.941,534.391,211.345,541.783,215.047,545.478L218.749,549.174"></path><path marker-end="url(#mermaid-9b1cc3a2-278f-49f9-a45b-9fb34bdd50bb_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_SIMTemplate_Modules_19" d="M358.712,453.632L330.017,446.526C301.321,439.421,243.929,425.211,205.359,412.277C166.789,399.344,147.04,387.689,137.166,381.861L127.292,376.033"></path><path marker-end="url(#mermaid-9b1cc3a2-278f-49f9-a45b-9fb34bdd50bb_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_SIMTemplate_Utility_20" d="M490.066,502L497.018,506.167C503.971,510.333,517.876,518.667,546.023,528.52C574.171,538.374,616.56,549.748,637.755,555.435L658.949,561.123"></path><path marker-end="url(#mermaid-9b1cc3a2-278f-49f9-a45b-9fb34bdd50bb_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_SIMTemplate_Backend_21" d="M500.45,502L509.005,506.167C517.56,510.333,534.671,518.667,539.71,526.518C544.75,534.369,537.719,541.737,534.203,545.422L530.687,549.106"></path><path marker-end="url(#mermaid-9b1cc3a2-278f-49f9-a45b-9fb34bdd50bb_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_IPC_Message_22" d="M1791.906,153.096L1759.349,159.413C1726.792,165.73,1661.677,178.365,1629.12,188.183C1596.563,198,1596.563,205,1596.563,208.5L1596.563,212"></path><path marker-end="url(#mermaid-9b1cc3a2-278f-49f9-a45b-9fb34bdd50bb_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_IPC_Server_23" d="M1850.45,166L1848.274,170.167C1846.098,174.333,1841.746,182.667,1839.57,190.333C1837.394,198,1837.394,205,1837.394,208.5L1837.394,212"></path><path marker-end="url(#mermaid-9b1cc3a2-278f-49f9-a45b-9fb34bdd50bb_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_IPC_Client_24" d="M1937.194,155.326L1963.649,161.272C1990.104,167.217,2043.015,179.109,2069.47,188.554C2095.925,198,2095.925,205,2095.925,208.5L2095.925,212"></path><path marker-end="url(#mermaid-9b1cc3a2-278f-49f9-a45b-9fb34bdd50bb_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Decoder_RegGroup_25" d="M2496.706,153.826L2467.755,160.022C2438.804,166.218,2380.902,178.609,2351.951,188.304C2323,198,2323,205,2323,208.5L2323,212"></path><path marker-end="url(#mermaid-9b1cc3a2-278f-49f9-a45b-9fb34bdd50bb_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Decoder_Constants_26" d="M2551.887,166L2549.711,170.167C2547.535,174.333,2543.183,182.667,2541.007,190.333C2538.831,198,2538.831,205,2538.831,208.5L2538.831,212"></path><path marker-end="url(#mermaid-9b1cc3a2-278f-49f9-a45b-9fb34bdd50bb_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Decoder_TensorInfo_27" d="M2635.269,157.628L2655.954,163.19C2676.64,168.752,2718.01,179.876,2738.696,188.938C2759.381,198,2759.381,205,2759.381,208.5L2759.381,212"></path><path marker-end="url(#mermaid-9b1cc3a2-278f-49f9-a45b-9fb34bdd50bb_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Backend_PyTorch_28" d="M502.162,606L502.162,612.167C502.162,618.333,502.162,630.667,502.162,642.333C502.162,654,502.162,665,502.162,670.5L502.162,676"></path><path marker-end="url(#mermaid-9b1cc3a2-278f-49f9-a45b-9fb34bdd50bb_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Visualize_Matplotlib_29" d="M1346.263,270L1346.263,274.167C1346.263,278.333,1346.263,286.667,1346.263,294.333C1346.263,302,1346.263,309,1346.263,312.5L1346.263,316"></path><path marker-end="url(#mermaid-9b1cc3a2-278f-49f9-a45b-9fb34bdd50bb_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Utility_NumPy_30" d="M725.575,606L725.575,612.167C725.575,618.333,725.575,630.667,725.575,642.333C725.575,654,725.575,665,725.575,670.5L725.575,676"></path><path marker-end="url(#mermaid-9b1cc3a2-278f-49f9-a45b-9fb34bdd50bb_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_SIMInput_SIMTemplate_31" d="M561.781,374L561.781,380.167C561.781,386.333,561.781,398.667,551.115,410.68C540.448,422.692,519.115,434.385,508.449,440.231L497.782,446.077"></path><path marker-end="url(#mermaid-9b1cc3a2-278f-49f9-a45b-9fb34bdd50bb_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_GlobalSettings_NPUCore_32" d="M341.512,570.956L426.103,563.63C510.694,556.304,679.875,541.652,764.466,525.659C849.056,509.667,849.056,492.333,849.056,473C849.056,453.667,849.056,432.333,849.056,411C849.056,389.667,849.056,368.333,849.056,349C849.056,329.667,849.056,312.333,812.262,297.232C775.468,282.131,701.879,269.262,665.085,262.828L628.29,256.394"></path><path marker-end="url(#mermaid-9b1cc3a2-278f-49f9-a45b-9fb34bdd50bb_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_GlobalSettings_Modules_33" d="M175.086,552L163.737,547.833C152.388,543.667,129.691,535.333,118.342,522.5C106.994,509.667,106.994,492.333,106.994,473C106.994,453.667,106.994,432.333,104.484,416.108C101.974,399.882,96.955,388.764,94.445,383.205L91.935,377.646"></path><path marker-end="url(#mermaid-9b1cc3a2-278f-49f9-a45b-9fb34bdd50bb_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_GlobalSettings_NoC_34" d="M236.435,606L233.651,612.167C230.867,618.333,225.299,630.667,227.861,642.514C230.423,654.362,241.114,665.725,246.46,671.406L251.806,677.087"></path></g><g class="edgeLabels"><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g transform="translate(849.0562534332275, 411)" class="edgeLabel"><g transform="translate(-37.78750228881836, -12)" class="label"><foreignObject height="24" width="75.57500457763672"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>Configures</p></span></div></foreignObject></g></g><g transform="translate(106.99374961853027, 475)" class="edgeLabel"><g transform="translate(-37.78750228881836, -12)" class="label"><foreignObject height="24" width="75.57500457763672"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>Configures</p></span></div></foreignObject></g></g><g transform="translate(219.73124885559082, 643)" class="edgeLabel"><g transform="translate(-37.78750228881836, -12)" class="label"><foreignObject height="24" width="75.57500457763672"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>Configures</p></span></div></foreignObject></g></g></g><g class="nodes"><g transform="translate(1864.550012588501, 35)" id="flowchart-Demo-620" class="node default entryPoint"><rect height="54" width="156.9875030517578" y="-27" x="-78.4937515258789" style="fill:#e1f5fe !important;stroke:#01579b !important" class="basic label-container"></rect><g transform="translate(-48.493751525878906, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="96.98750305175781"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>npu_demo.py</p></span></div></foreignObject></g></g><g transform="translate(1060.303129196167, 139)" id="flowchart-SIMTop-621" class="node default entryPoint"><rect height="54" width="130.9875030517578" y="-27" x="-65.4937515258789" style="fill:#e1f5fe !important;stroke:#01579b !important" class="basic label-container"></rect><g transform="translate(-35.493751525878906, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="70.98750305175781"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>SIMTop.py</p></span></div></foreignObject></g></g><g transform="translate(1864.550012588501, 139)" id="flowchart-IPC-623" class="node default"><rect height="54" width="145.2874984741211" y="-27" x="-72.64374923706055" style="" class="basic label-container"></rect><g transform="translate(-42.64374923706055, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="85.2874984741211"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>IPC Modules</p></span></div></foreignObject></g></g><g transform="translate(2565.9875049591064, 139)" id="flowchart-Decoder-625" class="node default"><rect height="54" width="138.5625" y="-27" x="-69.28125" style="" class="basic label-container"></rect><g transform="translate(-39.28125, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="78.5625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>decoder.py</p></span></div></foreignObject></g></g><g transform="translate(551.7000026702881, 243)" id="flowchart-NPUCore-627" class="node default coreModule"><rect height="54" width="145.3000030517578" y="-27" x="-72.6500015258789" style="fill:#fff3e0 !important;stroke:#e65100 !important" class="basic label-container"></rect><g transform="translate(-42.650001525878906, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="85.30000305175781"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>NPUCore.py</p></span></div></foreignObject></g></g><g transform="translate(279.953125, 707)" id="flowchart-NoC-629" class="node default coreModule"><rect height="54" width="111.04999923706055" y="-27" x="-55.52499961853027" style="fill:#fff3e0 !important;stroke:#e65100 !important" class="basic label-container"></rect><g transform="translate(-25.524999618530273, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="51.04999923706055"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>NoC.py</p></span></div></foreignObject></g></g><g transform="translate(445.0125026702881, 475)" id="flowchart-SIMTemplate-631" class="node default coreModule"><rect height="54" width="172.5999984741211" y="-27" x="-86.29999923706055" style="fill:#fff3e0 !important;stroke:#e65100 !important" class="basic label-container"></rect><g transform="translate(-56.29999923706055, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="112.5999984741211"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>SIMTemplate.py</p></span></div></foreignObject></g></g><g transform="translate(248.625, 579)" id="flowchart-GlobalSettings-633" class="node default config"><rect height="54" width="185.7750015258789" y="-27" x="-92.88750076293945" style="fill:#fce4ec !important;stroke:#880e4f !important" class="basic label-container"></rect><g transform="translate(-62.88750076293945, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="125.7750015258789"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>GlobalSettings.py</p></span></div></foreignObject></g></g><g transform="translate(1094.518756866455, 243)" id="flowchart-GoldenModel-635" class="node default"><rect height="54" width="175.4875030517578" y="-27" x="-87.7437515258789" style="" class="basic label-container"></rect><g transform="translate(-57.743751525878906, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="115.48750305175781"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>GoldenModel.py</p></span></div></foreignObject></g></g><g transform="translate(502.1624984741211, 579)" id="flowchart-Backend-637" class="node default hwModule"><rect height="54" width="221.3000030517578" y="-27" x="-110.6500015258789" style="fill:#f3e5f5 !important;stroke:#4a148c !important" class="basic label-container"></rect><g transform="translate(-80.6500015258789, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="161.3000030517578"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>sim_backend_torch.py</p></span></div></foreignObject></g></g><g transform="translate(1346.262508392334, 243)" id="flowchart-Visualize-639" class="node default"><rect height="54" width="145.5875015258789" y="-27" x="-72.79375076293945" style="" class="basic label-container"></rect><g transform="translate(-42.79375076293945, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="85.5875015258789"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Visualize.py</p></span></div></foreignObject></g></g><g transform="translate(725.5750007629395, 579)" id="flowchart-Utility-641" class="node default"><rect height="54" width="125.5250015258789" y="-27" x="-62.76250076293945" style="" class="basic label-container"></rect><g transform="translate(-32.76250076293945, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="65.5250015258789"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Utility.py</p></span></div></foreignObject></g></g><g transform="translate(78.0999984741211, 347)" id="flowchart-Modules-643" class="node default hwModule"><rect height="54" width="140.20000457763672" y="-27" x="-70.10000228881836" style="fill:#f3e5f5 !important;stroke:#4a148c !important" class="basic label-container"></rect><g transform="translate(-40.10000228881836, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="80.20000457763672"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Modules.py</p></span></div></foreignObject></g></g><g transform="translate(1596.5625114440918, 243)" id="flowchart-Message-665" class="node default"><rect height="54" width="172.5999984741211" y="-27" x="-86.29999923706055" style="" class="basic label-container"></rect><g transform="translate(-56.29999923706055, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="112.5999984741211"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>ipc_message.py</p></span></div></foreignObject></g></g><g transform="translate(1837.3937644958496, 243)" id="flowchart-Server-667" class="node default"><rect height="54" width="209.0625" y="-27" x="-104.53125" style="" class="basic label-container"></rect><g transform="translate(-74.53125, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="149.0625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>ipc_socket_server.py</p></span></div></foreignObject></g></g><g transform="translate(2095.9250144958496, 243)" id="flowchart-Client-669" class="node default"><rect height="54" width="208" y="-27" x="-104" style="" class="basic label-container"></rect><g transform="translate(-74, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="148"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>ipc_socket_client.py</p></span></div></foreignObject></g></g><g transform="translate(2323.000011444092, 243)" id="flowchart-RegGroup-671" class="node default"><rect height="54" width="146.1500015258789" y="-27" x="-73.07500076293945" style="" class="basic label-container"></rect><g transform="translate(-43.07500076293945, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="86.1500015258789"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>reggroup.py</p></span></div></foreignObject></g></g><g transform="translate(2538.831256866455, 243)" id="flowchart-Constants-673" class="node default"><rect height="54" width="185.51250457763672" y="-27" x="-92.75625228881836" style="" class="basic label-container"></rect><g transform="translate(-62.75625228881836, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="125.51250457763672"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>npu_constants.py</p></span></div></foreignObject></g></g><g transform="translate(2759.3812522888184, 243)" id="flowchart-TensorInfo-675" class="node default"><rect height="54" width="155.5875015258789" y="-27" x="-77.79375076293945" style="" class="basic label-container"></rect><g transform="translate(-47.79375076293945, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="95.5875015258789"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>tensorinfo.py</p></span></div></foreignObject></g></g><g transform="translate(502.1624984741211, 707)" id="flowchart-PyTorch-677" class="node default external"><rect height="54" width="170.7125015258789" y="-27" x="-85.35625076293945" style="fill:#e8f5e8 !important;stroke:#1b5e20 !important" class="basic label-container"></rect><g transform="translate(-55.35625076293945, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="110.7125015258789"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>PyTorch Library</p></span></div></foreignObject></g></g><g transform="translate(1346.262508392334, 347)" id="flowchart-Matplotlib-679" class="node default external"><rect height="54" width="188" y="-27" x="-94" style="fill:#e8f5e8 !important;stroke:#1b5e20 !important" class="basic label-container"></rect><g transform="translate(-64, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="128"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Matplotlib Library</p></span></div></foreignObject></g></g><g transform="translate(725.5750007629395, 707)" id="flowchart-NumPy-681" class="node default external"><rect height="54" width="164.1875" y="-27" x="-82.09375" style="fill:#e8f5e8 !important;stroke:#1b5e20 !important" class="basic label-container"></rect><g transform="translate(-52.09375, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="104.1875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>NumPy Library</p></span></div></foreignObject></g></g><g transform="translate(561.7812519073486, 347)" id="flowchart-SIMInput-682" class="node default"><rect height="54" width="143.375" y="-27" x="-71.6875" style="" class="basic label-container"></rect><g transform="translate(-41.6875, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="83.375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>SIMInput.py</p></span></div></foreignObject></g></g></g></g></g></svg>