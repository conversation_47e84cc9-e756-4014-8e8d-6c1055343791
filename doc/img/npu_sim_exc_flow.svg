<svg aria-roledescription="flowchart-v2" role="graphics-document document" viewBox="0 0 1416.012451171875 3529.199951171875" style="max-width: 1416.012451171875px;" class="flowchart" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns="http://www.w3.org/2000/svg" width="100%" id="mermaid-1aeba096-ffa2-49f6-b454-174bcff1b469"><style>#mermaid-1aeba096-ffa2-49f6-b454-174bcff1b469{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;fill:#ccc;}#mermaid-1aeba096-ffa2-49f6-b454-174bcff1b469 .error-icon{fill:#a44141;}#mermaid-1aeba096-ffa2-49f6-b454-174bcff1b469 .error-text{fill:#ddd;stroke:#ddd;}#mermaid-1aeba096-ffa2-49f6-b454-174bcff1b469 .edge-thickness-normal{stroke-width:1px;}#mermaid-1aeba096-ffa2-49f6-b454-174bcff1b469 .edge-thickness-thick{stroke-width:3.5px;}#mermaid-1aeba096-ffa2-49f6-b454-174bcff1b469 .edge-pattern-solid{stroke-dasharray:0;}#mermaid-1aeba096-ffa2-49f6-b454-174bcff1b469 .edge-thickness-invisible{stroke-width:0;fill:none;}#mermaid-1aeba096-ffa2-49f6-b454-174bcff1b469 .edge-pattern-dashed{stroke-dasharray:3;}#mermaid-1aeba096-ffa2-49f6-b454-174bcff1b469 .edge-pattern-dotted{stroke-dasharray:2;}#mermaid-1aeba096-ffa2-49f6-b454-174bcff1b469 .marker{fill:lightgrey;stroke:lightgrey;}#mermaid-1aeba096-ffa2-49f6-b454-174bcff1b469 .marker.cross{stroke:lightgrey;}#mermaid-1aeba096-ffa2-49f6-b454-174bcff1b469 svg{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;}#mermaid-1aeba096-ffa2-49f6-b454-174bcff1b469 p{margin:0;}#mermaid-1aeba096-ffa2-49f6-b454-174bcff1b469 .label{font-family:"trebuchet ms",verdana,arial,sans-serif;color:#ccc;}#mermaid-1aeba096-ffa2-49f6-b454-174bcff1b469 .cluster-label text{fill:#F9FFFE;}#mermaid-1aeba096-ffa2-49f6-b454-174bcff1b469 .cluster-label span{color:#F9FFFE;}#mermaid-1aeba096-ffa2-49f6-b454-174bcff1b469 .cluster-label span p{background-color:transparent;}#mermaid-1aeba096-ffa2-49f6-b454-174bcff1b469 .label text,#mermaid-1aeba096-ffa2-49f6-b454-174bcff1b469 span{fill:#ccc;color:#ccc;}#mermaid-1aeba096-ffa2-49f6-b454-174bcff1b469 .node rect,#mermaid-1aeba096-ffa2-49f6-b454-174bcff1b469 .node circle,#mermaid-1aeba096-ffa2-49f6-b454-174bcff1b469 .node ellipse,#mermaid-1aeba096-ffa2-49f6-b454-174bcff1b469 .node polygon,#mermaid-1aeba096-ffa2-49f6-b454-174bcff1b469 .node path{fill:#1f2020;stroke:#ccc;stroke-width:1px;}#mermaid-1aeba096-ffa2-49f6-b454-174bcff1b469 .rough-node .label text,#mermaid-1aeba096-ffa2-49f6-b454-174bcff1b469 .node .label text,#mermaid-1aeba096-ffa2-49f6-b454-174bcff1b469 .image-shape .label,#mermaid-1aeba096-ffa2-49f6-b454-174bcff1b469 .icon-shape .label{text-anchor:middle;}#mermaid-1aeba096-ffa2-49f6-b454-174bcff1b469 .node .katex path{fill:#000;stroke:#000;stroke-width:1px;}#mermaid-1aeba096-ffa2-49f6-b454-174bcff1b469 .rough-node .label,#mermaid-1aeba096-ffa2-49f6-b454-174bcff1b469 .node .label,#mermaid-1aeba096-ffa2-49f6-b454-174bcff1b469 .image-shape .label,#mermaid-1aeba096-ffa2-49f6-b454-174bcff1b469 .icon-shape .label{text-align:center;}#mermaid-1aeba096-ffa2-49f6-b454-174bcff1b469 .node.clickable{cursor:pointer;}#mermaid-1aeba096-ffa2-49f6-b454-174bcff1b469 .root .anchor path{fill:lightgrey!important;stroke-width:0;stroke:lightgrey;}#mermaid-1aeba096-ffa2-49f6-b454-174bcff1b469 .arrowheadPath{fill:lightgrey;}#mermaid-1aeba096-ffa2-49f6-b454-174bcff1b469 .edgePath .path{stroke:lightgrey;stroke-width:2.0px;}#mermaid-1aeba096-ffa2-49f6-b454-174bcff1b469 .flowchart-link{stroke:lightgrey;fill:none;}#mermaid-1aeba096-ffa2-49f6-b454-174bcff1b469 .edgeLabel{background-color:hsl(0, 0%, 34.4117647059%);text-align:center;}#mermaid-1aeba096-ffa2-49f6-b454-174bcff1b469 .edgeLabel p{background-color:hsl(0, 0%, 34.4117647059%);}#mermaid-1aeba096-ffa2-49f6-b454-174bcff1b469 .edgeLabel rect{opacity:0.5;background-color:hsl(0, 0%, 34.4117647059%);fill:hsl(0, 0%, 34.4117647059%);}#mermaid-1aeba096-ffa2-49f6-b454-174bcff1b469 .labelBkg{background-color:rgba(87.75, 87.75, 87.75, 0.5);}#mermaid-1aeba096-ffa2-49f6-b454-174bcff1b469 .cluster rect{fill:hsl(180, 1.5873015873%, 28.3529411765%);stroke:rgba(255, 255, 255, 0.25);stroke-width:1px;}#mermaid-1aeba096-ffa2-49f6-b454-174bcff1b469 .cluster text{fill:#F9FFFE;}#mermaid-1aeba096-ffa2-49f6-b454-174bcff1b469 .cluster span{color:#F9FFFE;}#mermaid-1aeba096-ffa2-49f6-b454-174bcff1b469 div.mermaidTooltip{position:absolute;text-align:center;max-width:200px;padding:2px;font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:12px;background:hsl(20, 1.5873015873%, 12.3529411765%);border:1px solid rgba(255, 255, 255, 0.25);border-radius:2px;pointer-events:none;z-index:100;}#mermaid-1aeba096-ffa2-49f6-b454-174bcff1b469 .flowchartTitleText{text-anchor:middle;font-size:18px;fill:#ccc;}#mermaid-1aeba096-ffa2-49f6-b454-174bcff1b469 rect.text{fill:none;stroke-width:0;}#mermaid-1aeba096-ffa2-49f6-b454-174bcff1b469 .icon-shape,#mermaid-1aeba096-ffa2-49f6-b454-174bcff1b469 .image-shape{background-color:hsl(0, 0%, 34.4117647059%);text-align:center;}#mermaid-1aeba096-ffa2-49f6-b454-174bcff1b469 .icon-shape p,#mermaid-1aeba096-ffa2-49f6-b454-174bcff1b469 .image-shape p{background-color:hsl(0, 0%, 34.4117647059%);padding:2px;}#mermaid-1aeba096-ffa2-49f6-b454-174bcff1b469 .icon-shape rect,#mermaid-1aeba096-ffa2-49f6-b454-174bcff1b469 .image-shape rect{opacity:0.5;background-color:hsl(0, 0%, 34.4117647059%);fill:hsl(0, 0%, 34.4117647059%);}#mermaid-1aeba096-ffa2-49f6-b454-174bcff1b469 :root{--mermaid-font-family:"trebuchet ms",verdana,arial,sans-serif;}</style><g><marker orient="auto" markerHeight="8" markerWidth="8" markerUnits="userSpaceOnUse" refY="5" refX="5" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-1aeba096-ffa2-49f6-b454-174bcff1b469_flowchart-v2-pointEnd"><path style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 0 L 10 5 L 0 10 z"></path></marker><marker orient="auto" markerHeight="8" markerWidth="8" markerUnits="userSpaceOnUse" refY="5" refX="4.5" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-1aeba096-ffa2-49f6-b454-174bcff1b469_flowchart-v2-pointStart"><path style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 5 L 10 10 L 10 0 z"></path></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5" refX="11" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-1aeba096-ffa2-49f6-b454-174bcff1b469_flowchart-v2-circleEnd"><circle style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"></circle></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5" refX="-1" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-1aeba096-ffa2-49f6-b454-174bcff1b469_flowchart-v2-circleStart"><circle style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"></circle></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5.2" refX="12" viewBox="0 0 11 11" class="marker cross flowchart-v2" id="mermaid-1aeba096-ffa2-49f6-b454-174bcff1b469_flowchart-v2-crossEnd"><path style="stroke-width: 2; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 l 9,9 M 10,1 l -9,9"></path></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5.2" refX="-1" viewBox="0 0 11 11" class="marker cross flowchart-v2" id="mermaid-1aeba096-ffa2-49f6-b454-174bcff1b469_flowchart-v2-crossStart"><path style="stroke-width: 2; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 l 9,9 M 10,1 l -9,9"></path></marker><g class="root"><g class="clusters"></g><g class="edgePaths"><path marker-end="url(#mermaid-1aeba096-ffa2-49f6-b454-174bcff1b469_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_A_B_0" d="M1230.834,62L1230.834,66.167C1230.834,70.333,1230.834,78.667,1230.834,86.333C1230.834,94,1230.834,101,1230.834,104.5L1230.834,108"></path><path marker-end="url(#mermaid-1aeba096-ffa2-49f6-b454-174bcff1b469_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_B_C_1" d="M1230.834,166L1230.834,170.167C1230.834,174.333,1230.834,182.667,1230.834,190.333C1230.834,198,1230.834,205,1230.834,208.5L1230.834,212"></path><path marker-end="url(#mermaid-1aeba096-ffa2-49f6-b454-174bcff1b469_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_C_D_2" d="M1230.834,294L1230.834,298.167C1230.834,302.333,1230.834,310.667,1230.834,318.333C1230.834,326,1230.834,333,1230.834,336.5L1230.834,340"></path><path marker-end="url(#mermaid-1aeba096-ffa2-49f6-b454-174bcff1b469_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_D_E_3" d="M1230.834,422L1230.834,426.167C1230.834,430.333,1230.834,438.667,1230.834,446.333C1230.834,454,1230.834,461,1230.834,464.5L1230.834,468"></path><path marker-end="url(#mermaid-1aeba096-ffa2-49f6-b454-174bcff1b469_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_E_F_4" d="M1230.834,526L1230.834,530.167C1230.834,534.333,1230.834,542.667,1230.834,550.333C1230.834,558,1230.834,565,1230.834,568.5L1230.834,572"></path><path marker-end="url(#mermaid-1aeba096-ffa2-49f6-b454-174bcff1b469_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_F_G_5" d="M1230.834,654L1230.834,658.167C1230.834,662.333,1230.834,670.667,1230.834,678.333C1230.834,686,1230.834,693,1230.834,696.5L1230.834,700"></path><path marker-end="url(#mermaid-1aeba096-ffa2-49f6-b454-174bcff1b469_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_G_H_6" d="M1100.834,759.247L1021.146,769.205C941.458,779.164,782.082,799.082,703.225,816.255C624.367,833.428,626.029,847.857,626.859,855.071L627.69,862.285"></path><path marker-end="url(#mermaid-1aeba096-ffa2-49f6-b454-174bcff1b469_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_H_I_7" d="M637.906,1060.725L637.823,1066.808C637.74,1072.892,637.573,1085.058,637.49,1096.642C637.406,1108.225,637.406,1119.225,637.406,1124.725L637.406,1130.225"></path><path marker-end="url(#mermaid-1aeba096-ffa2-49f6-b454-174bcff1b469_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_H_G_8" d="M717.909,936.503L788.386,916.919C858.863,897.335,999.818,858.168,1077.093,832.847C1154.368,807.527,1167.965,796.053,1174.763,790.316L1181.561,784.58"></path><path marker-end="url(#mermaid-1aeba096-ffa2-49f6-b454-174bcff1b469_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_I_J_9" d="M637.406,1188.225L637.406,1192.392C637.406,1196.558,637.406,1204.892,637.477,1212.642C637.547,1220.392,637.687,1227.559,637.758,1231.142L637.828,1234.726"></path><path marker-end="url(#mermaid-1aeba096-ffa2-49f6-b454-174bcff1b469_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_J_K_10" d="M576.562,1331.368L512.228,1347.676C447.894,1363.983,319.227,1396.598,254.893,1420.405C190.559,1444.213,190.559,1459.213,190.559,1466.713L190.559,1474.213"></path><path marker-end="url(#mermaid-1aeba096-ffa2-49f6-b454-174bcff1b469_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_J_L_11" d="M593.995,1348.802L576.013,1362.204C558.031,1375.605,522.067,1402.409,504.085,1423.311C486.103,1444.213,486.103,1459.213,486.103,1466.713L486.103,1474.213"></path><path marker-end="url(#mermaid-1aeba096-ffa2-49f6-b454-174bcff1b469_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_J_M_12" d="M681.817,1348.802L699.632,1362.204C717.448,1375.605,753.079,1402.409,770.894,1421.311C788.709,1440.213,788.709,1451.213,788.709,1456.713L788.709,1462.213"></path><path marker-end="url(#mermaid-1aeba096-ffa2-49f6-b454-174bcff1b469_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_J_N_13" d="M699.195,1331.424L763.041,1347.722C826.887,1364.02,954.58,1396.616,1018.426,1420.414C1082.272,1444.213,1082.272,1459.213,1082.272,1466.713L1082.272,1474.213"></path><path marker-end="url(#mermaid-1aeba096-ffa2-49f6-b454-174bcff1b469_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_K_O_14" d="M190.559,1532.213L190.559,1538.379C190.559,1544.546,190.559,1556.879,201.685,1566.99C212.811,1577.1,235.062,1584.988,246.188,1588.932L257.314,1592.876"></path><path marker-end="url(#mermaid-1aeba096-ffa2-49f6-b454-174bcff1b469_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_L_O_15" d="M486.103,1532.213L486.103,1538.379C486.103,1544.546,486.103,1556.879,474.805,1566.993C463.507,1577.106,440.911,1585,429.613,1588.947L418.315,1592.893"></path><path marker-end="url(#mermaid-1aeba096-ffa2-49f6-b454-174bcff1b469_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_O_P_16" d="M337.25,1648.213L337.25,1652.379C337.25,1656.546,337.25,1664.879,337.25,1672.546C337.25,1680.213,337.25,1687.213,337.25,1690.713L337.25,1694.213"></path><path marker-end="url(#mermaid-1aeba096-ffa2-49f6-b454-174bcff1b469_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_P_Q_17" d="M337.25,1752.213L337.25,1756.379C337.25,1760.546,337.25,1768.879,337.25,1776.546C337.25,1784.213,337.25,1791.213,337.25,1794.713L337.25,1798.213"></path><path marker-end="url(#mermaid-1aeba096-ffa2-49f6-b454-174bcff1b469_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Q_R_18" d="M337.25,1856.213L337.25,1860.379C337.25,1864.546,337.25,1872.879,337.25,1880.546C337.25,1888.213,337.25,1895.213,337.25,1898.713L337.25,1902.213"></path><path marker-end="url(#mermaid-1aeba096-ffa2-49f6-b454-174bcff1b469_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_R_S_19" d="M337.25,1984.213L337.25,1988.379C337.25,1992.546,337.25,2000.879,337.25,2008.546C337.25,2016.213,337.25,2023.213,337.25,2026.713L337.25,2030.213"></path><path marker-end="url(#mermaid-1aeba096-ffa2-49f6-b454-174bcff1b469_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_S_T_20" d="M297.977,2088.213L291.916,2092.379C285.855,2096.546,273.734,2104.879,267.673,2112.546C261.612,2120.213,261.612,2127.213,261.612,2130.713L261.612,2134.213"></path><path marker-end="url(#mermaid-1aeba096-ffa2-49f6-b454-174bcff1b469_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_T_U_21" d="M261.612,2192.213L261.612,2196.379C261.612,2200.546,261.612,2208.879,261.683,2216.629C261.753,2224.379,261.894,2231.546,261.964,2235.13L262.034,2238.713"></path><path marker-end="url(#mermaid-1aeba096-ffa2-49f6-b454-174bcff1b469_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_U_V_22" d="M215.059,2393.084L202.216,2407.01C189.373,2420.935,163.686,2448.786,150.843,2468.212C138,2487.638,138,2498.638,138,2504.138L138,2509.638"></path><path marker-end="url(#mermaid-1aeba096-ffa2-49f6-b454-174bcff1b469_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_U_W_23" d="M338.199,2364.051L401.499,2382.815C464.799,2401.58,591.398,2439.109,654.697,2468.54C717.997,2497.971,717.997,2519.304,717.997,2538.638C717.997,2557.971,717.997,2575.304,717.997,2592.638C717.997,2609.971,717.997,2627.304,717.997,2646.638C717.997,2665.971,717.997,2687.304,717.997,2708.638C717.997,2729.971,717.997,2751.304,717.997,2770.638C717.997,2789.971,717.997,2807.304,717.997,2826.638C717.997,2845.971,717.997,2867.304,717.997,2888.638C717.997,2909.971,717.997,2931.304,717.997,2945.471C717.997,2959.638,717.997,2966.638,717.997,2970.138L717.997,2973.638"></path><path marker-end="url(#mermaid-1aeba096-ffa2-49f6-b454-174bcff1b469_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_V_X_24" d="M138,2567.638L138,2571.804C138,2575.971,138,2584.304,138,2591.971C138,2599.638,138,2606.638,138,2610.138L138,2613.638"></path><path marker-end="url(#mermaid-1aeba096-ffa2-49f6-b454-174bcff1b469_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_X_Y_25" d="M138,2671.638L138,2677.804C138,2683.971,138,2696.304,138,2707.971C138,2719.638,138,2730.638,138,2736.138L138,2741.638"></path><path marker-end="url(#mermaid-1aeba096-ffa2-49f6-b454-174bcff1b469_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Y_Z_26" d="M138,2799.638L138,2803.804C138,2807.971,138,2816.304,138,2823.971C138,2831.638,138,2838.638,138,2842.138L138,2845.638"></path><path marker-end="url(#mermaid-1aeba096-ffa2-49f6-b454-174bcff1b469_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Z_AA_27" d="M138,2927.638L138,2931.804C138,2935.971,138,2944.304,138,2951.971C138,2959.638,138,2966.638,138,2970.138L138,2973.638"></path><path marker-end="url(#mermaid-1aeba096-ffa2-49f6-b454-174bcff1b469_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_AA_BB_28" d="M138,3031.638L138,3035.804C138,3039.971,138,3048.304,160.383,3066.888C182.766,3085.473,227.531,3114.308,249.914,3128.726L272.297,3143.143"></path><path marker-end="url(#mermaid-1aeba096-ffa2-49f6-b454-174bcff1b469_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_BB_S_29" d="M422.183,3165.49L495.781,3147.348C569.379,3129.206,716.574,3092.922,790.171,3066.113C863.769,3039.304,863.769,3021.971,863.769,3004.638C863.769,2987.304,863.769,2969.971,863.769,2950.638C863.769,2931.304,863.769,2909.971,863.769,2888.638C863.769,2867.304,863.769,2845.971,863.769,2826.638C863.769,2807.304,863.769,2789.971,863.769,2770.638C863.769,2751.304,863.769,2729.971,863.769,2708.638C863.769,2687.304,863.769,2665.971,863.769,2646.638C863.769,2627.304,863.769,2609.971,863.769,2592.638C863.769,2575.304,863.769,2557.971,863.769,2538.638C863.769,2519.304,863.769,2497.971,863.769,2464.685C863.769,2431.4,863.769,2386.163,863.769,2342.925C863.769,2299.688,863.769,2258.45,863.769,2229.165C863.769,2199.879,863.769,2182.546,863.769,2165.213C863.769,2147.879,863.769,2130.546,794.298,2115.018C724.827,2099.49,585.885,2085.768,516.414,2078.907L446.943,2072.046"></path><path marker-end="url(#mermaid-1aeba096-ffa2-49f6-b454-174bcff1b469_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_BB_CC_30" d="M338.831,3289.7L338.748,3295.783C338.665,3301.867,338.498,3314.033,338.415,3325.617C338.331,3337.2,338.331,3348.2,338.331,3353.7L338.331,3359.2"></path><path marker-end="url(#mermaid-1aeba096-ffa2-49f6-b454-174bcff1b469_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_W_BB_31" d="M717.997,3031.638L717.997,3035.804C717.997,3039.971,717.997,3048.304,668.349,3069.421C618.702,3090.538,519.407,3124.439,469.759,3141.39L420.112,3158.34"></path><path marker-end="url(#mermaid-1aeba096-ffa2-49f6-b454-174bcff1b469_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_CC_DD_32" d="M338.331,3417.2L338.331,3421.367C338.331,3425.533,338.331,3433.867,484.356,3445.822C630.381,3457.777,922.431,3473.355,1068.456,3481.143L1214.481,3488.932"></path><path marker-end="url(#mermaid-1aeba096-ffa2-49f6-b454-174bcff1b469_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_DD_G_33" d="M1356.731,3467.2L1363.442,3463.033C1370.153,3458.867,1383.575,3450.533,1390.286,3437.7C1396.997,3424.867,1396.997,3407.533,1396.997,3388.2C1396.997,3368.867,1396.997,3347.533,1396.997,3313.403C1396.997,3279.273,1396.997,3232.346,1396.997,3187.419C1396.997,3142.492,1396.997,3099.565,1396.997,3069.434C1396.997,3039.304,1396.997,3021.971,1396.997,3004.638C1396.997,2987.304,1396.997,2969.971,1396.997,2950.638C1396.997,2931.304,1396.997,2909.971,1396.997,2888.638C1396.997,2867.304,1396.997,2845.971,1396.997,2826.638C1396.997,2807.304,1396.997,2789.971,1396.997,2770.638C1396.997,2751.304,1396.997,2729.971,1396.997,2708.638C1396.997,2687.304,1396.997,2665.971,1396.997,2646.638C1396.997,2627.304,1396.997,2609.971,1396.997,2592.638C1396.997,2575.304,1396.997,2557.971,1396.997,2538.638C1396.997,2519.304,1396.997,2497.971,1396.997,2464.685C1396.997,2431.4,1396.997,2386.163,1396.997,2342.925C1396.997,2299.688,1396.997,2258.45,1396.997,2229.165C1396.997,2199.879,1396.997,2182.546,1396.997,2165.213C1396.997,2147.879,1396.997,2130.546,1396.997,2113.213C1396.997,2095.879,1396.997,2078.546,1396.997,2061.213C1396.997,2043.879,1396.997,2026.546,1396.997,2007.213C1396.997,1987.879,1396.997,1966.546,1396.997,1945.213C1396.997,1923.879,1396.997,1902.546,1396.997,1883.213C1396.997,1863.879,1396.997,1846.546,1396.997,1829.213C1396.997,1811.879,1396.997,1794.546,1396.997,1777.213C1396.997,1759.879,1396.997,1742.546,1396.997,1725.213C1396.997,1707.879,1396.997,1690.546,1396.997,1673.213C1396.997,1655.879,1396.997,1638.546,1396.997,1621.213C1396.997,1603.879,1396.997,1586.546,1396.997,1567.213C1396.997,1547.879,1396.997,1526.546,1396.997,1503.213C1396.997,1479.879,1396.997,1454.546,1396.997,1422.88C1396.997,1391.215,1396.997,1353.217,1396.997,1317.219C1396.997,1281.221,1396.997,1247.223,1396.997,1221.557C1396.997,1195.892,1396.997,1178.558,1396.997,1159.225C1396.997,1139.892,1396.997,1118.558,1396.997,1084.706C1396.997,1050.854,1396.997,1004.483,1396.997,958.113C1396.997,911.742,1396.997,865.371,1384.121,836.296C1371.244,807.221,1345.492,795.443,1332.616,789.553L1319.74,783.664"></path><path marker-end="url(#mermaid-1aeba096-ffa2-49f6-b454-174bcff1b469_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_M_EE_34" d="M788.709,1544.213L788.709,1548.379C788.709,1552.546,788.709,1560.879,788.709,1568.546C788.709,1576.213,788.709,1583.213,788.709,1586.713L788.709,1590.213"></path><path marker-end="url(#mermaid-1aeba096-ffa2-49f6-b454-174bcff1b469_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_EE_FF_35" d="M788.709,1648.213L788.709,1652.379C788.709,1656.546,788.709,1664.879,816.225,1673.988C843.741,1683.097,898.772,1692.982,926.288,1697.925L953.804,1702.867"></path><path marker-end="url(#mermaid-1aeba096-ffa2-49f6-b454-174bcff1b469_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_FF_G_36" d="M1198.178,1698.213L1216.693,1694.046C1235.207,1689.879,1272.236,1681.546,1290.751,1668.713C1309.266,1655.879,1309.266,1638.546,1309.266,1621.213C1309.266,1603.879,1309.266,1586.546,1309.266,1567.213C1309.266,1547.879,1309.266,1526.546,1309.266,1503.213C1309.266,1479.879,1309.266,1454.546,1309.266,1422.88C1309.266,1391.215,1309.266,1353.217,1309.266,1317.219C1309.266,1281.221,1309.266,1247.223,1309.266,1221.557C1309.266,1195.892,1309.266,1178.558,1309.266,1159.225C1309.266,1139.892,1309.266,1118.558,1309.266,1084.706C1309.266,1050.854,1309.266,1004.483,1309.266,958.113C1309.266,911.742,1309.266,865.371,1303.38,836.483C1297.495,807.595,1285.725,796.189,1279.84,790.486L1273.955,784.784"></path><path marker-end="url(#mermaid-1aeba096-ffa2-49f6-b454-174bcff1b469_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_N_GG_37" d="M1082.272,1532.213L1082.272,1538.379C1082.272,1544.546,1082.272,1556.879,1087.678,1566.83C1093.084,1576.781,1103.895,1584.35,1109.301,1588.134L1114.707,1591.919"></path><path marker-end="url(#mermaid-1aeba096-ffa2-49f6-b454-174bcff1b469_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_GG_G_38" d="M1195.122,1594.213L1201.074,1590.046C1207.026,1585.879,1218.93,1577.546,1224.882,1562.713C1230.834,1547.879,1230.834,1526.546,1230.834,1503.213C1230.834,1479.879,1230.834,1454.546,1230.834,1422.88C1230.834,1391.215,1230.834,1353.217,1230.834,1317.219C1230.834,1281.221,1230.834,1247.223,1230.834,1221.557C1230.834,1195.892,1230.834,1178.558,1230.834,1159.225C1230.834,1139.892,1230.834,1118.558,1230.834,1084.706C1230.834,1050.854,1230.834,1004.483,1230.834,958.113C1230.834,911.742,1230.834,865.371,1230.834,836.685C1230.834,808,1230.834,797,1230.834,791.5L1230.834,786"></path></g><g class="edgeLabels"><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g transform="translate(637.4062461853027, 1097.2250061035156)" class="edgeLabel"><g transform="translate(-11.324999809265137, -12)" class="label"><foreignObject height="24" width="22.649999618530273"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>Yes</p></span></div></foreignObject></g></g><g transform="translate(1140.771870136261, 819)" class="edgeLabel"><g transform="translate(-9.40000057220459, -12)" class="label"><foreignObject height="24" width="18.80000114440918"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>No</p></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g transform="translate(190.55937576293945, 1429.2125091552734)" class="edgeLabel"><g transform="translate(-41.08124923706055, -12)" class="label"><foreignObject height="24" width="82.1624984741211"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>NICE_INSTR</p></span></div></foreignObject></g></g><g transform="translate(486.10312271118164, 1429.2125091552734)" class="edgeLabel"><g transform="translate(-45.78125, -12)" class="label"><foreignObject height="24" width="91.5625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>VNICE_INSTR</p></span></div></foreignObject></g></g><g transform="translate(788.7093696594238, 1429.2125091552734)" class="edgeLabel"><g transform="translate(-23.700000762939453, -12)" class="label"><foreignObject height="24" width="47.400001525878906"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>CHECK</p></span></div></foreignObject></g></g><g transform="translate(1082.2718696594238, 1429.2125091552734)" class="edgeLabel"><g transform="translate(-43.73125076293945, -12)" class="label"><foreignObject height="24" width="87.4625015258789"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>NICE_READY</p></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g transform="translate(138, 2476.6375122070312)" class="edgeLabel"><g transform="translate(-11.324999809265137, -12)" class="label"><foreignObject height="24" width="22.649999618530273"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>Yes</p></span></div></foreignObject></g></g><g transform="translate(717.9968724250793, 2708.6375122070312)" class="edgeLabel"><g transform="translate(-9.40000057220459, -12)" class="label"><foreignObject height="24" width="18.80000114440918"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>No</p></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g transform="translate(863.7687458992004, 2644.6375122070312)" class="edgeLabel"><g transform="translate(-9.40000057220459, -12)" class="label"><foreignObject height="24" width="18.80000114440918"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>No</p></span></div></foreignObject></g></g><g transform="translate(338.33124923706055, 3326.2000122070312)" class="edgeLabel"><g transform="translate(-11.324999809265137, -12)" class="label"><foreignObject height="24" width="22.649999618530273"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>Yes</p></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g></g><g class="nodes"><g transform="translate(1230.8343696594238, 35)" id="flowchart-A-538" class="node default"><rect height="54" width="202.6875" y="-27" x="-101.34375" style="fill:#e1f5fe !important" class="basic label-container"></rect><g transform="translate(-71.34375, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="142.6875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Start: npu_demo.py</p></span></div></foreignObject></g></g><g transform="translate(1230.8343696594238, 139)" id="flowchart-B-539" class="node default"><rect height="54" width="202.0500030517578" y="-27" x="-101.0250015258789" style="" class="basic label-container"></rect><g transform="translate(-71.0250015258789, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="142.0500030517578"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Initialize Subsystem</p></span></div></foreignObject></g></g><g transform="translate(1230.8343696594238, 255)" id="flowchart-C-541" class="node default"><rect height="78" width="260" y="-39" x="-130" style="" class="basic label-container"></rect><g transform="translate(-100, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Create PNMDie with NPU cores</p></span></div></foreignObject></g></g><g transform="translate(1230.8343696594238, 383)" id="flowchart-D-543" class="node default"><rect height="78" width="260" y="-39" x="-130" style="" class="basic label-container"></rect><g transform="translate(-100, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Initialize NoC mesh network</p></span></div></foreignObject></g></g><g transform="translate(1230.8343696594238, 499)" id="flowchart-E-545" class="node default"><rect height="54" width="251.0124969482422" y="-27" x="-125.5062484741211" style="" class="basic label-container"></rect><g transform="translate(-95.5062484741211, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="191.0124969482422"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Setup IPC server for MoSIM</p></span></div></foreignObject></g></g><g transform="translate(1230.8343696594238, 615)" id="flowchart-F-547" class="node default"><rect height="78" width="260" y="-39" x="-130" style="" class="basic label-container"></rect><g transform="translate(-100, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Data import from input files</p></span></div></foreignObject></g></g><g transform="translate(1230.8343696594238, 743)" id="flowchart-G-549" class="node default"><rect height="78" width="260" y="-39" x="-130" style="" class="basic label-container"></rect><g transform="translate(-100, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Wait for instruction requests</p></span></div></foreignObject></g></g><g transform="translate(637.4062461853027, 958.1125030517578)" id="flowchart-H-551" class="node default"><polygon transform="translate(-102.11250305175781,102.11250305175781)" class="label-container" points="102.11250305175781,0 204.22500610351562,-102.11250305175781 102.11250305175781,-204.22500610351562 0,-102.11250305175781"></polygon><g transform="translate(-75.11250305175781, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="150.22500610351562"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Receive IPC Request?</p></span></div></foreignObject></g></g><g transform="translate(637.4062461853027, 1161.2250061035156)" id="flowchart-I-553" class="node default"><rect height="54" width="231.4499969482422" y="-27" x="-115.7249984741211" style="" class="basic label-container"></rect><g transform="translate(-85.7249984741211, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="171.4499969482422"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Decode instruction type</p></span></div></foreignObject></g></g><g transform="translate(637.4062461853027, 1315.2187576293945)" id="flowchart-J-557" class="node default"><polygon transform="translate(-76.9937515258789,76.9937515258789)" class="label-container" points="76.9937515258789,0 153.9875030517578,-76.9937515258789 76.9937515258789,-153.9875030517578 0,-76.9937515258789"></polygon><g transform="translate(-49.993751525878906, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="99.98750305175781"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Request Type?</p></span></div></foreignObject></g></g><g transform="translate(190.55937576293945, 1505.2125091552734)" id="flowchart-K-559" class="node default"><rect height="54" width="245.875" y="-27" x="-122.9375" style="" class="basic label-container"></rect><g transform="translate(-92.9375, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="185.875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Decode RISC-V instruction</p></span></div></foreignObject></g></g><g transform="translate(486.10312271118164, 1505.2125091552734)" id="flowchart-L-561" class="node default"><rect height="54" width="245.21250915527344" y="-27" x="-122.60625457763672" style="" class="basic label-container"></rect><g transform="translate(-92.60625457763672, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="185.21250915527344"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Decode vector instruction</p></span></div></foreignObject></g></g><g transform="translate(788.7093696594238, 1505.2125091552734)" id="flowchart-M-563" class="node default"><rect height="78" width="260" y="-39" x="-130" style="" class="basic label-container"></rect><g transform="translate(-100, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Check for completed operations</p></span></div></foreignObject></g></g><g transform="translate(1082.2718696594238, 1505.2125091552734)" id="flowchart-N-565" class="node default"><rect height="54" width="227.125" y="-27" x="-113.5625" style="" class="basic label-container"></rect><g transform="translate(-83.5625, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="167.125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Return readiness status</p></span></div></foreignObject></g></g><g transform="translate(337.25, 1621.2125091552734)" id="flowchart-O-567" class="node default"><rect height="54" width="205.6125030517578" y="-27" x="-102.8062515258789" style="" class="basic label-container"></rect><g transform="translate(-72.8062515258789, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="145.6125030517578"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Convert to primitive</p></span></div></foreignObject></g></g><g transform="translate(337.25, 1725.2125091552734)" id="flowchart-P-571" class="node default"><rect height="54" width="214.66250610351562" y="-27" x="-107.33125305175781" style="" class="basic label-container"></rect><g transform="translate(-77.33125305175781, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="154.66250610351562"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Add to request queue</p></span></div></foreignObject></g></g><g transform="translate(337.25, 1829.2125091552734)" id="flowchart-Q-573" class="node default"><rect height="54" width="245.875" y="-27" x="-122.9375" style="" class="basic label-container"></rect><g transform="translate(-92.9375, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="185.875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Execute until target cycle</p></span></div></foreignObject></g></g><g transform="translate(337.25, 1945.2125091552734)" id="flowchart-R-575" class="node default"><rect height="78" width="260" y="-39" x="-130" style="fill:#fff3e0 !important" class="basic label-container"></rect><g transform="translate(-100, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Simulation Loop: run_simulation_until</p></span></div></foreignObject></g></g><g transform="translate(337.25, 2061.2125091552734)" id="flowchart-S-577" class="node default"><rect height="54" width="211.4250030517578" y="-27" x="-105.7125015258789" style="" class="basic label-container"></rect><g transform="translate(-75.7125015258789, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="151.4250030517578"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Update all NPU cores</p></span></div></foreignObject></g></g><g transform="translate(261.61249923706055, 2165.2125091552734)" id="flowchart-T-579" class="node default"><rect height="54" width="236.875" y="-27" x="-118.4375" style="" class="basic label-container"></rect><g transform="translate(-88.4375, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="176.875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Check if all cores stalled</p></span></div></foreignObject></g></g><g transform="translate(261.61249923706055, 2340.9250106811523)" id="flowchart-U-581" class="node default"><polygon transform="translate(-98.7125015258789,98.7125015258789)" class="label-container" points="98.7125015258789,0 197.4250030517578,-98.7125015258789 98.7125015258789,-197.4250030517578 0,-98.7125015258789"></polygon><g transform="translate(-71.7125015258789, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="143.4250030517578"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Primitives in queue?</p></span></div></foreignObject></g></g><g transform="translate(138, 2540.6375122070312)" id="flowchart-V-583" class="node default"><rect height="54" width="253.5124969482422" y="-27" x="-126.7562484741211" style="" class="basic label-container"></rect><g transform="translate(-96.7562484741211, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="193.5124969482422"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Dispatch primitive to cores</p></span></div></foreignObject></g></g><g transform="translate(717.9968724250793, 3004.6375122070312)" id="flowchart-W-585" class="node default"><rect height="54" width="206.21250915527344" y="-27" x="-103.10625457763672" style="" class="basic label-container"></rect><g transform="translate(-73.10625457763672, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="146.21250915527344"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Calculate next cycle</p></span></div></foreignObject></g></g><g transform="translate(138, 2644.6375122070312)" id="flowchart-X-587" class="node default"><rect height="54" width="237.91250610351562" y="-27" x="-118.95625305175781" style="fill:#f3e5f5 !important" class="basic label-container"></rect><g transform="translate(-88.95625305175781, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="177.91250610351562"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Cores execute primitives</p></span></div></foreignObject></g></g><g transform="translate(138, 2772.6375122070312)" id="flowchart-Y-589" class="node default"><rect height="54" width="217.0625" y="-27" x="-108.53125" style="" class="basic label-container"></rect><g transform="translate(-78.53125, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="157.0625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Update module states</p></span></div></foreignObject></g></g><g transform="translate(138, 2888.6375122070312)" id="flowchart-Z-591" class="node default"><rect height="78" width="260" y="-39" x="-130" style="" class="basic label-container"></rect><g transform="translate(-100, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Record energy/performance</p></span></div></foreignObject></g></g><g transform="translate(138, 3004.6375122070312)" id="flowchart-AA-593" class="node default"><rect height="54" width="188.2375030517578" y="-27" x="-94.1187515258789" style="" class="basic label-container"></rect><g transform="translate(-64.1187515258789, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="128.2375030517578"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Check completion</p></span></div></foreignObject></g></g><g transform="translate(338.33124923706055, 3185.4187622070312)" id="flowchart-BB-595" class="node default"><polygon transform="translate(-103.78125,103.78125)" class="label-container" points="103.78125,0 207.5625,-103.78125 103.78125,-207.5625 0,-103.78125"></polygon><g transform="translate(-76.78125, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="153.5625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Simulation complete?</p></span></div></foreignObject></g></g><g transform="translate(338.33124923706055, 3390.2000122070312)" id="flowchart-CC-599" class="node default"><rect height="54" width="194.40000915527344" y="-27" x="-97.20000457763672" style="fill:#e8f5e8 !important" class="basic label-container"></rect><g transform="translate(-67.20000457763672, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="134.40000915527344"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Generate response</p></span></div></foreignObject></g></g><g transform="translate(1313.2437481880188, 3494.2000122070312)" id="flowchart-DD-603" class="node default"><rect height="54" width="189.53750610351562" y="-27" x="-94.76875305175781" style="" class="basic label-container"></rect><g transform="translate(-64.76875305175781, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="129.53750610351562"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Send IPC response</p></span></div></foreignObject></g></g><g transform="translate(788.7093696594238, 1621.2125091552734)" id="flowchart-EE-607" class="node default"><rect height="54" width="219.40000915527344" y="-27" x="-109.70000457763672" style="" class="basic label-container"></rect><g transform="translate(-79.70000457763672, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="159.40000915527344"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Check response queue</p></span></div></foreignObject></g></g><g transform="translate(1078.2031211853027, 1725.2125091552734)" id="flowchart-FF-609" class="node default"><rect height="54" width="240.9250030517578" y="-27" x="-120.4625015258789" style="" class="basic label-container"></rect><g transform="translate(-90.4625015258789, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="180.9250030517578"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Return completed results</p></span></div></foreignObject></g></g><g transform="translate(1156.5531196594238, 1621.2125091552734)" id="flowchart-GG-613" class="node default"><rect height="54" width="203.72500610351562" y="-27" x="-101.86250305175781" style="" class="basic label-container"></rect><g transform="translate(-71.86250305175781, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="143.72500610351562"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Return queue status</p></span></div></foreignObject></g></g></g></g></g></svg>