# * * * * * * * * * * * * * * * * #
# Visualizing the execution of Primitives on NPU Core
# Author: <PERSON><PERSON><PERSON>
# * * * * * * * * * * * * * * * * #

import numpy as np
import matplotlib.pyplot as plt


def visualize(total_cycles, visual_title, ldst_cycles=[], tp_cycles=[], vp_cycles=[], tm_cycles=[], noc_cycles=[],
              save=False, figsize=(12, 2)):
    # 初始化周期数组，全部初始化为空余周期（0表示白色）
    # 创建一个二维数组，第一行为 Load/Store，第二行为 Tensor Processing, 第三行为 Vector Processing,
    # 第四行为 Tensor Manupulation, 第五行为 NOC
    cycles = np.zeros((5, total_cycles), dtype=int)

    colors = ['white', 'lightcoral', 'darkseagreen', 'skyblue', 'sandybrown', 'plum']

    for i in range(len(cycles)):
        cycles[i][0] = 0

    cycles[0, 0] = 1                    # 防止出现bug
    # 标记 Load/Store 周期为 1（红色）
    for start, end in ldst_cycles:
        cycles[0, start:end + 1] = 1

    cycles[1, 0] = 2                    # 防止出现bug
    # 标记 Tensor Processing 周期为 2（绿色）
    for start, end in tp_cycles:
        cycles[1, start:end + 1] = 2

    cycles[2, 0] = 3                    # 防止出现bug
    # 标记 Vector Processing 周期为 3（蓝色）
    for start, end in vp_cycles:
        cycles[2, start:end + 1] = 3

    cycles[3, 0] = 4                    # 防止出现bug
    # 标记 Tensor Manipulation 周期为 4（橙色）
    for start, end in tm_cycles:
        cycles[3, start:end + 1] = 4

    cycles[4, 0] = 5                    # 防止出现bug
    # 标记 NOC 周期为 5（紫色）
    for start, end in noc_cycles:
        cycles[4, start:end + 1] = 5

    # 绘制可视化图
    fig, ax = plt.subplots(figsize=figsize)  # 调整图像高度以适应两行
    cmap = plt.cm.colors.ListedColormap(colors)  # 定义颜色映射
    ax.imshow(cycles, aspect='auto', cmap=cmap, interpolation='nearest')

    # 设置坐标轴
    ax.set_yticks([0, 1, 2, 3, 4])  # 设置两行的刻度
    ax.set_yticklabels(['LD/ST', 'TP', 'VP', 'TM', 'NOC'])  # 设置两行的标签
    ax.set_xticks(range(0, total_cycles, 1000))  # 每隔 1k 有一个刻度线
    ax.set_xticklabels(['' if (i % 5000) != 0 else f'{i / 1000:.0f}' for i in range(0, total_cycles, 1000)],
                       fontsize=10)  # 每隔 5k 显示一个标签
    ax.set_xlabel('Cycle (k)')
    ax.set_title(visual_title)

    # 添加图例
    legend_elements = [
        plt.Line2D([0], [0], marker='s', color='w', label='LSU', markerfacecolor=colors[1], markersize=10),
        plt.Line2D([0], [0], marker='s', color='w', label='MPU', markerfacecolor=colors[2], markersize=10),
        plt.Line2D([0], [0], marker='s', color='w', label='VPU', markerfacecolor=colors[3], markersize=10),
        plt.Line2D([0], [0], marker='s', color='w', label='TMU', markerfacecolor=colors[4], markersize=10),
        plt.Line2D([0], [0], marker='s', color='w', label='NOC', markerfacecolor=colors[5], markersize=10),
        plt.Line2D([0], [0], marker='s', color='w', label='Idle', markerfacecolor=colors[0], markersize=10)
    ]
    ax.legend(handles=legend_elements, loc='upper right', bbox_to_anchor=(1.12, 1.06), title='Primitive')

    # 调整布局，增加顶部和底部的间距
    plt.subplots_adjust(top=0.88, bottom=0.2, left=0.06)  # 调整顶部和底部的间距

    # # 每次打印 100 个元素
    # print('hey')
    # for i in range(0, len(cycles[1]), 100):
    #     print(cycles[1][i:i + 100])
    # print('heyhey')
    # for i in range(0, len(cycles[2]), 100):
    #     print(cycles[2][i:i + 100])

    if save:
        plt.savefig('./inout/core_timeline/' + visual_title + '.png', dpi=600)
    else:
        plt.show()

