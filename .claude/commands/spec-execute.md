# Spec Execute Command

Execute specific tasks from the approved task list.

## Usage
```
/spec-execute [task-id] [feature-name]
```

## Phase Overview
**Your Role**: Execute tasks systematically with validation

This is Phase 4 of the spec workflow. Your goal is to implement individual tasks from the approved task list, one at a time.

## Instructions

**Agent-Based Execution (Recommended)**: If the `spec-task-executor` agent is available, use it for optimal task implementation:

```
Use the spec-task-executor agent to implement the specified task for the {feature-name} specification.

The agent should:
1. Load all specification context from .claude/specs/{feature-name}/
2. Load steering documents from .claude/steering/ (if available)  
3. Implement ONLY the specified task
4. Follow all project conventions and leverage existing code
5. Mark the task as complete in tasks.md
6. Provide a completion summary

Context files to load:
- .claude/specs/{feature-name}/requirements.md
- .claude/specs/{feature-name}/design.md
- .claude/specs/{feature-name}/tasks.md  
- .claude/steering/product.md (if exists)
- .claude/steering/tech.md (if exists)
- .claude/steering/structure.md (if exists)

Task to implement: {task-id}
```

**Manual Execution (Fallback)**: If the agent is not available, follow this process:

1. **Prerequisites**
   - Ensure tasks.md exists and is approved
   - Load the spec documents from `.claude/specs/{feature-name}/`:
     - Load `.claude/specs/{feature-name}/requirements.md` for feature requirements
     - Load `.claude/specs/{feature-name}/design.md` for technical design
     - Load `.claude/specs/{feature-name}/tasks.md` for the complete task list
   - **Load all steering documents** (if available): 
     - Load .claude/steering/product.md for product context
     - Load .claude/steering/tech.md for technical patterns
     - Load .claude/steering/structure.md for project conventions
   - Identify the specific task to execute

2. **Process**
   1. Load spec documents from `.claude/specs/{feature-name}/` directory:
      - Load requirements.md, design.md, and tasks.md for complete context
   2. Execute ONLY the specified task (never multiple tasks)
   3. Implement following existing code patterns and conventions
   4. Validate implementation against referenced requirements
   5. Run tests and checks if applicable
   6. **CRITICAL**: Mark task as complete by changing [ ] to [x] in tasks.md
   7. Confirm task completion status to user
   8. **CRITICAL**: Stop and wait for user review before proceeding

3. **Task Execution**
   - Focus on ONE task at a time
   - If task has sub-tasks, start with those
   - Follow the implementation details from design.md
   - Verify against requirements specified in the task

4. **Implementation Guidelines**
   - Write clean, maintainable code
   - **Follow steering documents**: Adhere to patterns in tech.md and conventions in structure.md
   - Follow existing code patterns and conventions
   - Include appropriate error handling
   - Add unit tests where specified
   - Document complex logic

5. **Validation**
   - Verify implementation meets acceptance criteria
   - Run tests if they exist
   - Check for lint/type errors
   - Ensure integration with existing code

6. **Task Completion Protocol**
When completing any task during `/spec-execute`:
   1. **Update tasks.md**: Change task status from `- [ ]` to `- [x]`
   2. **Confirm to user**: State clearly "Task X has been marked as complete"
   3. **Stop execution**: Do not proceed to next task automatically
   4. **Wait for instruction**: Let user decide next steps

7. **Test Generation (if agent available)**
During or after task implementation, use the `spec-test-generator` agent:

```
Use the spec-test-generator agent to generate tests for task {task-id} of the {feature-name} specification.

The agent should:
1. Load requirements.md for acceptance criteria
2. Load design.md for technical details
3. Analyze existing test patterns in the codebase
4. Generate comprehensive test cases
5. Provide test implementations following project conventions

The generated tests ensure comprehensive coverage of the implemented functionality.
```

8. **Post-Implementation Review (if agent available)**
After marking a task complete, use the `spec-task-implementation-reviewer` agent:

```
Use the spec-task-implementation-reviewer agent to review the implementation of task {task-id} for the {feature-name} specification.

The agent should:
1. Load all specification documents from .claude/specs/{feature-name}/
2. Load steering documents from .claude/steering/ (if available)
3. Review the implementation for correctness and compliance
4. Provide structured feedback on the implementation quality
5. Identify any issues that need to be addressed

Context files to review:
- .claude/specs/{feature-name}/requirements.md
- .claude/specs/{feature-name}/design.md
- .claude/specs/{feature-name}/tasks.md
- Implementation changes for task {task-id}
```

9. **Integration Testing (if agent available)**
After implementation review passes, use the `spec-integration-tester` agent:

```
Use the spec-integration-tester agent to test the implementation of task {task-id} for the {feature-name} specification.

The agent should:
1. Load all specification documents and understand the changes made
2. Run relevant test suites for the implemented functionality
3. Validate integration points and API contracts
4. Check for regressions using git history analysis
5. Provide comprehensive test feedback

Test context:
- Changes made in task {task-id}
- Related test suites to execute
- Integration points to validate
- Git history for regression analysis
```

## Critical Workflow Rules

### Task Execution
- **ONLY** execute one task at a time during implementation
- **CRITICAL**: Mark completed tasks as [x] in tasks.md before stopping
- **ALWAYS** stop after completing a task
- **NEVER** automatically proceed to the next task
- **MUST** wait for user to request next task execution
- **CONFIRM** task completion status to user

### Requirement References
- **ALL** tasks must reference specific requirements using _Requirements: X.Y_ format
- **ENSURE** traceability from requirements through design to implementation
- **VALIDATE** implementations against referenced requirements

## Task Selection
If no task-id specified:
- Look at tasks.md for the spec
- Recommend the next pending task
- Ask user to confirm before proceeding

If no feature-name specified:
- Check `.claude/specs/` directory for available specs
- If only one spec exists, use it
- If multiple specs exist, ask user which one to use
- Display error if no specs are found

## Examples
```
/spec-execute 1 user-authentication
/spec-execute 2.1 user-authentication
```

## Important Rules
- Only execute ONE task at a time
- **ALWAYS** mark completed tasks as [x] in tasks.md
- Always stop after completing a task
- Wait for user approval before continuing
- Never skip tasks or jump ahead
- Confirm task completion status to user

## Next Steps
After task completion, you can:
- Review the implementation (automated if spec-task-implementation-reviewer agent is available)
- Address any issues identified in the review
- Run tests if applicable
- Execute the next task using `/spec-execute [next-task-id]`
- Check overall progress with `/spec-status {feature-name}`
