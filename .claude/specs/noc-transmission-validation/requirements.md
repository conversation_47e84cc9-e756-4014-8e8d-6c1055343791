# NoC Transmission Validation and Implementation Requirements

## 1. Overview

### 1.1 Purpose
Enable complete Network-on-Chip (NoC) data transmission functionality in the PNM simulator, ensuring data can be correctly transferred between NPU cores from source memory addresses to destination memory addresses.

### 1.2 Background
The current NoC implementation has multiple critical issues:
1. **No actual data movement**: Infrastructure exists but data transfer is not implemented
2. **Incorrect resource allocation**: Both noc_src_drv and noc_dest_drv occupy resources from both cores
3. **Instruction type merging**: Source and destination instructions are incorrectly merged into single NOC primitive type
4. **Golden model incomplete**: Raises `NotImplementedError` for NoC operations

### 1.3 Scope
- Fix incorrect resource allocation where both instructions occupy both cores' resources
- Separate noc_src_drv and noc_dest_drv into distinct primitive types
- Implement actual memory-to-memory data transfer in NoC operations
- Complete the golden model validation for NoC primitives
- Ensure proper synchronization between source and destination cores
- Validate data integrity during transmission

## 2. User Stories

### 2.1 Data Transfer Between Cores
**As a** PNM simulator user  
**I want** to transfer tensor data from one NPU core's memory to another core's memory  
**So that** I can implement distributed neural network computations across multiple cores

### 2.2 NoC Instruction Execution
**As a** RISC-V processor interfacing with the NPU  
**I want** to issue NoC instructions (noc_src_drv, noc_dest_drv) that result in actual data movement  
**So that** I can orchestrate multi-core tensor operations

### 2.3 Data Integrity Validation
**As a** verification engineer  
**I want** the golden model to validate NoC data transfers  
**So that** I can ensure the simulator correctly models hardware behavior

### 2.4 Synchronization Guarantee
**As a** system architect  
**I want** proper handshaking between source and destination cores  
**So that** data transfers are reliable and prevent race conditions

## 3. Functional Requirements

### 3.1 Instruction Processing Fix
- **REQ-IP-001**: The system SHALL maintain separate primitive types for noc_src_drv (NOC_SRC) and noc_dest_drv (NOC_DEST)
- **REQ-IP-002**: NOC_SRC primitives SHALL only occupy source core resources (TX router, source memory, LSU if DRAM)
- **REQ-IP-003**: NOC_DEST primitives SHALL only occupy destination core resources (RX router, destination memory, LSU if DRAM)
- **REQ-IP-004**: The system SHALL correctly route instructions to appropriate cores based on mask/group settings
- **REQ-IP-005**: The system SHALL follow the correct NoC operation sequence: fence → config → dest_drv → src_drv

### 3.2 Data Transfer Implementation
- **REQ-DT-001**: The system SHALL copy data from source memory address to destination memory address when executing NoC primitives
- **REQ-DT-002**: The system SHALL support data transfers between any two cores in the 2D mesh network
- **REQ-DT-003**: The system SHALL handle different data sizes from single elements to full tensors
- **REQ-DT-004**: The system SHALL preserve data integrity during transmission

### 3.3 Memory Access
- **REQ-MA-001**: The system SHALL access source core's memory (SRAM/DRAM) at the specified base address
- **REQ-MA-002**: The system SHALL write to destination core's memory at the specified base address
- **REQ-MA-003**: The system SHALL validate memory addresses are within valid ranges before transfer
- **REQ-MA-004**: The system SHALL support both SRAM and DRAM as source/destination

### 3.4 Synchronization
- **REQ-SY-001**: The destination core SHALL be ready to receive data before source begins transmission
- **REQ-SY-002**: The system SHALL implement proper handshaking between source and destination cores
- **REQ-SY-003**: The system SHALL prevent data corruption from concurrent accesses
- **REQ-SY-004**: The system SHALL signal completion to both cores after transfer

### 3.5 Golden Model
- **REQ-GM-001**: The golden model SHALL implement NoC data transfer validation
- **REQ-GM-002**: The golden model SHALL compare source and destination data for correctness
- **REQ-GM-003**: The golden model SHALL track all NoC transfers for verification
- **REQ-GM-004**: The golden model SHALL report mismatches with detailed diagnostics

## 4. Non-Functional Requirements

### 4.1 Performance
- **REQ-PF-001**: Data transfer SHALL complete within the modeled cycle count based on data size and NoC bandwidth
- **REQ-PF-002**: The implementation SHALL not introduce artificial delays beyond hardware modeling

### 4.2 Accuracy
- **REQ-AC-001**: The simulator SHALL accurately model NoC bandwidth constraints (128-bit width)
- **REQ-AC-002**: The simulator SHALL correctly calculate transfer cycles based on data size

### 4.3 Maintainability
- **REQ-MT-001**: The implementation SHALL follow existing code patterns in the simulator
- **REQ-MT-002**: The code SHALL include clear documentation of the data transfer process

## 5. Acceptance Criteria

### 5.1 Resource Allocation Correctness
**GIVEN** a noc_src_drv instruction targeting Core[0,0]
**WHEN** the instruction is processed
**THEN** only Core[0,0]'s TX router and source memory are marked as occupied

### 5.2 Instruction Separation
**GIVEN** noc_src_drv and noc_dest_drv instructions
**WHEN** they are decoded
**THEN** they result in different primitive types (NOC_SRC and NOC_DEST)

### 5.3 Basic Data Transfer
**GIVEN** a NoC primitive with source and destination addresses  
**WHEN** the primitive is executed  
**THEN** data at the source address is copied to the destination address

### 5.4 Cross-Core Transfer
**GIVEN** source data in Core[0,0] SRAM at address 0x1000  
**WHEN** noc_src_drv and noc_dest_drv instructions are issued to transfer to Core[1,1] at address 0x2000  
**THEN** Core[1,1] SRAM at 0x2000 contains the same data as Core[0,0] SRAM at 0x1000

### 5.5 Golden Model Validation
**GIVEN** a NoC transfer operation  
**WHEN** the golden model executes  
**THEN** it validates the data was correctly transferred and reports any mismatches

### 5.6 Synchronization Verification
**GIVEN** concurrent NoC operations  
**WHEN** multiple transfers target the same destination  
**THEN** transfers complete sequentially without data corruption

### 5.7 Vector Transfer Support
**GIVEN** a VNICE instruction with multiple source-destination pairs  
**WHEN** the instruction is decoded and executed  
**THEN** all specified transfers complete successfully

## 6. Technical Constraints

### 6.1 Architecture Constraints
- Must work within existing 2D mesh NoC topology
- Must respect 128-bit NoC data width
- Must follow XY/YX routing algorithms

### 6.2 Implementation Constraints
- Must integrate with existing Module and Primitive architecture
- Must maintain cycle-accurate timing model
- Must preserve existing energy modeling

### 6.3 Compatibility
- Must maintain backward compatibility with existing test cases
- Must support both NICE and VNICE instruction variants

## 7. Risks and Mitigation

### 7.1 Breaking Changes
**Risk**: Separating NOC primitive types may break existing tests
**Mitigation**: Implement backward compatibility layer or update tests systematically

### 7.2 Data Coherency
**Risk**: Cache coherency issues between cores  
**Mitigation**: Implement proper memory barriers and synchronization

### 7.3 Deadlock Potential
**Risk**: Circular dependencies in multi-core transfers  
**Mitigation**: Maintain existing deadlock prevention in first_update_core logic

### 7.4 Performance Impact
**Risk**: Data copying overhead affects simulation speed  
**Mitigation**: Optimize memory operations and consider lazy evaluation

## 8. Dependencies

### 8.1 Existing Components
- NoC.py routing infrastructure
- NPUCore.py primitive execution framework
- Modules.py memory access interfaces
- decoder.py instruction parsing

### 8.2 External Interfaces
- IPC communication with MoSIM
- RISC-V instruction interface

## 9. Current Implementation Issues

### 9.1 Resource Allocation Bug
Current code incorrectly allocates resources for both cores regardless of instruction type:
```python
# Current wrong implementation:
prim.module_use.append(prim.noc_settings.src_memory)  # Always adds
prim.module_use.append(prim.noc_settings.dst_memory)  # Always adds
```

### 9.2 Instruction Type Merging
Both instructions map to same primitive:
```python
"noc_src_drv"  : PrimName.NOC
"noc_dest_drv" : PrimName.NOC
```

### 9.3 Broadcast Distribution
Same primitive sent to all cores, relying on mask/group filtering.

## 10. Future Considerations

### 10.1 DMA Enhancement
Consider implementing DMA-style transfers for improved efficiency

### 10.2 Multicast Support
Enable one-to-many data distribution for broadcast operations

### 10.3 QoS Features
Add quality-of-service for prioritized transfers

## 11. Success Metrics

### 11.1 Functional Completeness
- 100% of NoC primitives result in actual data transfer
- Golden model validates all NoC operations without NotImplementedError

### 11.2 Test Coverage
- All combinations of source/destination memory types tested
- Edge cases (full memory, invalid addresses) handled correctly

### 11.3 Performance Accuracy
- Transfer cycles match theoretical calculations within 5% margin