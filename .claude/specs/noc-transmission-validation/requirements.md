# NoC Transmission Validation and Implementation Requirements

## 1. Overview

### 1.1 Purpose
Enable complete Network-on-Chip (NoC) data transmission functionality in the PNM simulator, ensuring data can be correctly transferred between NPU cores from source memory addresses to destination memory addresses.

### 1.2 Background
The current NoC implementation provides infrastructure for routing and coordination but lacks actual data movement capabilities. The golden model raises `NotImplementedError` for NoC operations, indicating this critical functionality is incomplete.

### 1.3 Scope
- Implement actual memory-to-memory data transfer in NoC operations
- Complete the golden model validation for NoC primitives
- Ensure proper synchronization between source and destination cores
- Validate data integrity during transmission

## 2. User Stories

### 2.1 Data Transfer Between Cores
**As a** PNM simulator user  
**I want** to transfer tensor data from one NPU core's memory to another core's memory  
**So that** I can implement distributed neural network computations across multiple cores

### 2.2 NoC Instruction Execution
**As a** RISC-V processor interfacing with the NPU  
**I want** to issue NoC instructions (noc_src_drv, noc_dest_drv) that result in actual data movement  
**So that** I can orchestrate multi-core tensor operations

### 2.3 Data Integrity Validation
**As a** verification engineer  
**I want** the golden model to validate NoC data transfers  
**So that** I can ensure the simulator correctly models hardware behavior

### 2.4 Synchronization Guarantee
**As a** system architect  
**I want** proper handshaking between source and destination cores  
**So that** data transfers are reliable and prevent race conditions

## 3. Functional Requirements

### 3.1 Data Transfer Implementation
- **REQ-DT-001**: The system SHALL copy data from source memory address to destination memory address when executing NoC primitives
- **REQ-DT-002**: The system SHALL support data transfers between any two cores in the 2D mesh network
- **REQ-DT-003**: The system SHALL handle different data sizes from single elements to full tensors
- **REQ-DT-004**: The system SHALL preserve data integrity during transmission

### 3.2 Memory Access
- **REQ-MA-001**: The system SHALL access source core's memory (SRAM/DRAM) at the specified base address
- **REQ-MA-002**: The system SHALL write to destination core's memory at the specified base address
- **REQ-MA-003**: The system SHALL validate memory addresses are within valid ranges before transfer
- **REQ-MA-004**: The system SHALL support both SRAM and DRAM as source/destination

### 3.3 Synchronization
- **REQ-SY-001**: The destination core SHALL be ready to receive data before source begins transmission
- **REQ-SY-002**: The system SHALL implement proper handshaking between source and destination cores
- **REQ-SY-003**: The system SHALL prevent data corruption from concurrent accesses
- **REQ-SY-004**: The system SHALL signal completion to both cores after transfer

### 3.4 Golden Model
- **REQ-GM-001**: The golden model SHALL implement NoC data transfer validation
- **REQ-GM-002**: The golden model SHALL compare source and destination data for correctness
- **REQ-GM-003**: The golden model SHALL track all NoC transfers for verification
- **REQ-GM-004**: The golden model SHALL report mismatches with detailed diagnostics

## 4. Non-Functional Requirements

### 4.1 Performance
- **REQ-PF-001**: Data transfer SHALL complete within the modeled cycle count based on data size and NoC bandwidth
- **REQ-PF-002**: The implementation SHALL not introduce artificial delays beyond hardware modeling

### 4.2 Accuracy
- **REQ-AC-001**: The simulator SHALL accurately model NoC bandwidth constraints (128-bit width)
- **REQ-AC-002**: The simulator SHALL correctly calculate transfer cycles based on data size

### 4.3 Maintainability
- **REQ-MT-001**: The implementation SHALL follow existing code patterns in the simulator
- **REQ-MT-002**: The code SHALL include clear documentation of the data transfer process

## 5. Acceptance Criteria

### 5.1 Basic Data Transfer
**GIVEN** a NoC primitive with source and destination addresses  
**WHEN** the primitive is executed  
**THEN** data at the source address is copied to the destination address

### 5.2 Cross-Core Transfer
**GIVEN** source data in Core[0,0] SRAM at address 0x1000  
**WHEN** noc_src_drv and noc_dest_drv instructions are issued to transfer to Core[1,1] at address 0x2000  
**THEN** Core[1,1] SRAM at 0x2000 contains the same data as Core[0,0] SRAM at 0x1000

### 5.3 Golden Model Validation
**GIVEN** a NoC transfer operation  
**WHEN** the golden model executes  
**THEN** it validates the data was correctly transferred and reports any mismatches

### 5.4 Synchronization Verification
**GIVEN** concurrent NoC operations  
**WHEN** multiple transfers target the same destination  
**THEN** transfers complete sequentially without data corruption

### 5.5 Vector Transfer Support
**GIVEN** a VNICE instruction with multiple source-destination pairs  
**WHEN** the instruction is decoded and executed  
**THEN** all specified transfers complete successfully

## 6. Technical Constraints

### 6.1 Architecture Constraints
- Must work within existing 2D mesh NoC topology
- Must respect 128-bit NoC data width
- Must follow XY/YX routing algorithms

### 6.2 Implementation Constraints
- Must integrate with existing Module and Primitive architecture
- Must maintain cycle-accurate timing model
- Must preserve existing energy modeling

### 6.3 Compatibility
- Must maintain backward compatibility with existing test cases
- Must support both NICE and VNICE instruction variants

## 7. Risks and Mitigation

### 7.1 Data Coherency
**Risk**: Cache coherency issues between cores  
**Mitigation**: Implement proper memory barriers and synchronization

### 7.2 Deadlock Potential
**Risk**: Circular dependencies in multi-core transfers  
**Mitigation**: Maintain existing deadlock prevention in first_update_core logic

### 7.3 Performance Impact
**Risk**: Data copying overhead affects simulation speed  
**Mitigation**: Optimize memory operations and consider lazy evaluation

## 8. Dependencies

### 8.1 Existing Components
- NoC.py routing infrastructure
- NPUCore.py primitive execution framework
- Modules.py memory access interfaces
- decoder.py instruction parsing

### 8.2 External Interfaces
- IPC communication with MoSIM
- RISC-V instruction interface

## 9. Future Considerations

### 9.1 DMA Enhancement
Consider implementing DMA-style transfers for improved efficiency

### 9.2 Multicast Support
Enable one-to-many data distribution for broadcast operations

### 9.3 QoS Features
Add quality-of-service for prioritized transfers

## 10. Success Metrics

### 10.1 Functional Completeness
- 100% of NoC primitives result in actual data transfer
- Golden model validates all NoC operations without NotImplementedError

### 10.2 Test Coverage
- All combinations of source/destination memory types tested
- Edge cases (full memory, invalid addresses) handled correctly

### 10.3 Performance Accuracy
- Transfer cycles match theoretical calculations within 5% margin