# NoC Transmission Implementation Design

## 1. Overview

### 1.1 Purpose
This design document details the technical implementation for fixing NoC transmission issues in the PNM simulator, including primitive separation, resource allocation fixes, and golden model implementation.

### 1.2 Goals
- Separate NOC primitive into NOC_SRC and NOC_DEST types
- Fix resource allocation to match instruction semantics
- Implement actual data transfer in golden model
- Ensure proper synchronization between cores

### 1.3 Scope
Implementation changes across multiple modules: SIMTemplate.py, tensorinfo.py, NPUCore.py, Modules.py, and decoder.py.

## 2. Architecture Overview

```mermaid
graph TB
    subgraph "Instruction Flow"
        A[RISC-V Instructions] --> B[decoder.py]
        B --> C{Instruction Type}
        C -->|noc_src_drv| D[NOC_SRC Primitive]
        C -->|noc_dest_drv| E[NOC_DEST Primitive]
    end
    
    subgraph "Core Execution"
        D --> F[Source Core]
        E --> G[Destination Core]
        F -->|TX Router| H[NoC Network]
        H -->|RX Router| G
    end
    
    subgraph "Resource Allocation"
        F --> I[Source Memory + TX]
        G --> J[Dest Memory + RX]
    end
    
    subgraph "Golden Model"
        K[Golden VM System] --> L[Track Transfers]
        L --> M[Validate Data]
    end
```

## 3. Component Design

### 3.1 Primitive Type Separation

#### 3.1.1 SIMTemplate.py Updates
```python
class PrimName(Enum):
    # Remove "Fake" comment and activate existing types
    NOC_FENCE = auto()  
    NOC_SRC = auto()    # Source core primitive
    NOC_DEST = auto()   # Destination core primitive
    NOC = auto()        # Keep for backward compatibility (deprecated)
```

#### 3.1.2 tensorinfo.py Updates
```python
convert_PrimName = {
    "noc_src_drv"   : PrimName.NOC_SRC,
    "noc_dest_drv"  : PrimName.NOC_DEST,
    "noc_fence_drv" : PrimName.NOC_FENCE,
}
```

### 3.2 Resource Allocation Fix

#### 3.2.1 NPUCore.py Resource Management
```python
def prim_module_use(self, prim):
    # Define separate primitive lists
    noc_src_list = [SIMTemp.PrimName.NOC_SRC]
    noc_dest_list = [SIMTemp.PrimName.NOC_DEST]
    noc_fence_list = [SIMTemp.PrimName.NOC_FENCE]
    
    # NOC_SRC: Only source core resources
    elif prim.type in noc_src_list:
        if self.x == prim.noc_settings.src_core.x and self.y == prim.noc_settings.src_core.y:
            prim.module_use.append(self.noc_router_tx)
            prim.module_use.append(prim.noc_settings.src_memory)
            if isinstance(prim.noc_settings.src_memory, Mods.DRAMBank):
                prim.module_use.append(self.tensor_load_store_unit)
    
    # NOC_DEST: Only destination core resources
    elif prim.type in noc_dest_list:
        if self.x == prim.noc_settings.dst_core.x and self.y == prim.noc_settings.dst_core.y:
            prim.module_use.append(self.noc_router_rx)
            prim.module_use.append(prim.noc_settings.dst_memory)
            if isinstance(prim.noc_settings.dst_memory, Mods.DRAMBank):
                prim.module_use.append(self.tensor_load_store_unit)
    
    # NOC_FENCE: No resource allocation needed
    elif prim.type in noc_fence_list:
        pass  # Fence doesn't occupy resources
```

### 3.3 NoC Settings Enhancement

#### 3.3.1 SIMTemplate.py NoC Settings
```python
class NoCSettings:
    def __init__(self):
        self.src_group = None
        self.src_id = None
        self.dst_group = None
        self.dst_id = None
        self.src_memory = None
        self.dst_memory = None
        self.src_core = None
        self.dst_core = None
        self.transfer_size = 0
        self.transfer_complete = False  # New field for synchronization
        self.data_buffer = None         # New field for data transfer
```

### 3.4 Primitive Execution Logic

#### 3.4.1 NPUCore.py Execution Updates
```python
def prim_exe(self, prim):
    if prim.type == SIMTemp.PrimName.NOC_SRC:
        # Source core: Read data and prepare for transmission
        if self.x == prim.noc_settings.src_core.x and self.y == prim.noc_settings.src_core.y:
            # Read data from source memory
            data = self.read_memory_data(prim.noc_settings.src_memory, 
                                       prim.tensor_in1.byte_base,
                                       prim.noc_settings.transfer_size)
            # Store in NoC settings for transfer
            prim.noc_settings.data_buffer = data
            # Execute on TX router
            self.noc_router_tx.run_prim(prim)
    
    elif prim.type == SIMTemp.PrimName.NOC_DEST:
        # Destination core: Prepare to receive data
        if self.x == prim.noc_settings.dst_core.x and self.y == prim.noc_settings.dst_core.y:
            # Execute on RX router
            self.noc_router_rx.run_prim(prim)
            # Write data when available
            if prim.noc_settings.transfer_complete and prim.noc_settings.data_buffer:
                self.write_memory_data(prim.noc_settings.dst_memory,
                                     prim.tensor_out.byte_base,
                                     prim.noc_settings.data_buffer)
```

### 3.5 Golden Model Implementation

#### 3.5.1 SIMTemplate.py Golden Run
```python
def golden_run(self, core_id, golden_vm_system: sim_backend.GOLDEN_VM_SYSTEM):
    match self.type:
        case PrimName.NOC_SRC:
            # Track source operation
            desc_in = TensorInfo2Descriptor(self.tensor_in1)
            data = golden_vm_system.read_tensor(core_id, desc_in)
            # Store transfer info in golden system
            transfer_id = f"{self.noc_settings.src_core}_{self.noc_settings.dst_core}_{self.id}"
            golden_vm_system.noc_transfers[transfer_id] = {
                'src_core': self.noc_settings.src_core,
                'dst_core': self.noc_settings.dst_core,
                'src_addr': self.tensor_in1.byte_base,
                'dst_addr': None,  # Set by NOC_DEST
                'data': data,
                'size': self.noc_settings.transfer_size
            }
        
        case PrimName.NOC_DEST:
            # Track destination operation and validate
            transfer_id = f"{self.noc_settings.src_core}_{self.noc_settings.dst_core}_{self.id}"
            if transfer_id in golden_vm_system.noc_transfers:
                transfer = golden_vm_system.noc_transfers[transfer_id]
                transfer['dst_addr'] = self.tensor_out.byte_base
                # Write data to destination
                desc_out = TensorInfo2Descriptor(self.tensor_out)
                golden_vm_system.write_tensor(core_id, desc_out, transfer['data'])
                # Mark transfer complete
                transfer['complete'] = True
            else:
                raise ValueError(f"NoC transfer {transfer_id} not initiated by NOC_SRC")
        
        case PrimName.NOC_FENCE:
            # Verify all pending transfers are complete
            for tid, transfer in golden_vm_system.noc_transfers.items():
                if not transfer.get('complete', False):
                    raise ValueError(f"NoC transfer {tid} not complete at fence")
```

### 3.6 Synchronization Mechanism

#### 3.6.1 Transfer Coordination
```python
class NoCTransferManager:
    """Manages NoC transfers between cores"""
    def __init__(self):
        self.pending_transfers = {}  # transfer_id -> transfer_info
        self.completed_transfers = []
    
    def initiate_transfer(self, src_core, dst_core, data, size):
        transfer_id = f"{src_core}_{dst_core}_{time.time()}"
        self.pending_transfers[transfer_id] = {
            'src_core': src_core,
            'dst_core': dst_core,
            'data': data,
            'size': size,
            'status': 'initiated'
        }
        return transfer_id
    
    def complete_transfer(self, transfer_id):
        if transfer_id in self.pending_transfers:
            transfer = self.pending_transfers.pop(transfer_id)
            transfer['status'] = 'completed'
            self.completed_transfers.append(transfer)
            return transfer['data']
        return None
```

### 3.7 TensorInfo Updates

#### 3.7.1 tensorinfo.py Separate Handlers
```python
def fill_TensorInfo_noc_src(prim, info_dict):
    """Fill tensor info for NOC_SRC primitive"""
    # Source tensor setup
    prim.tensor_in1.strides = info_dict.get('noc.strides', [0, 0, 0])
    prim.tensor_in1.shape = info_dict.get('noc.shape', [1, 1, 1])
    prim.tensor_in1.byte_base = info_dict['noc.base_addr']
    # No output tensor for source
    prim.tensor_out = None

def fill_TensorInfo_noc_dest(prim, info_dict):
    """Fill tensor info for NOC_DEST primitive"""
    # Destination tensor setup
    prim.tensor_out.strides = info_dict.get('noc.strides', [0, 0, 0])
    prim.tensor_out.shape = info_dict.get('noc.shape', [1, 1, 1])
    prim.tensor_out.byte_base = info_dict['noc.base_addr']
    # No input tensor for destination
    prim.tensor_in1 = None
```

## 4. Implementation Flow

### 4.1 Instruction Sequence
1. **NOC_FENCE** - Ensure previous transfers complete
2. **NOC Configuration** - Set transfer parameters
3. **NOC_DEST** - Destination prepares to receive
4. **NOC_SRC** - Source initiates transfer
5. **Data Transfer** - Actual memory copy occurs

### 4.2 Data Flow
```mermaid
sequenceDiagram
    participant RC as RISC-V Controller
    participant SC as Source Core
    participant DC as Destination Core
    participant NoC as NoC Network
    participant GM as Golden Model
    
    RC->>DC: noc_dest_drv (prepare)
    DC->>DC: Allocate RX resources
    DC->>GM: Track destination ready
    
    RC->>SC: noc_src_drv (send)
    SC->>SC: Read memory data
    SC->>GM: Track source data
    SC->>NoC: Transmit via TX
    
    NoC->>DC: Route data
    DC->>DC: Write memory data
    DC->>GM: Validate transfer
    
    RC->>SC: noc_fence_drv
    SC->>GM: Verify complete
```

## 5. Testing Strategy

### 5.1 Unit Tests
- Test NOC_SRC primitive resource allocation
- Test NOC_DEST primitive resource allocation
- Test golden model data tracking
- Test transfer completion validation

### 5.2 Integration Tests
- Single core-to-core transfer
- Multiple concurrent transfers
- Different memory types (SRAM/DRAM)
- Various data sizes

### 5.3 Validation Tests
- Golden model comparison
- Data integrity verification
- Cycle count accuracy
- Energy calculation correctness

## 6. Migration Plan

### 6.1 Backward Compatibility
- Keep NOC primitive type for transition
- Add deprecation warnings
- Provide migration guide

### 6.2 Phased Implementation
1. **Phase 1**: Implement primitive separation
2. **Phase 2**: Fix resource allocation
3. **Phase 3**: Implement golden model
4. **Phase 4**: Add data transfer
5. **Phase 5**: Full testing and validation

## 7. Performance Considerations

### 7.1 Memory Efficiency
- Use lazy data copying where possible
- Implement zero-copy for same-memory transfers
- Cache frequently accessed data

### 7.2 Simulation Speed
- Minimize golden model overhead
- Batch small transfers
- Use efficient data structures

## 8. Error Handling

### 8.1 Resource Conflicts
- Detect and report resource allocation conflicts
- Provide clear error messages
- Suggest resolution strategies

### 8.2 Transfer Failures
- Track incomplete transfers
- Report at fence operations
- Provide debugging information

## 9. Documentation Updates

### 9.1 Code Documentation
- Document new primitive types
- Explain resource allocation logic
- Provide usage examples

### 9.2 User Documentation
- Update NoC operation guide
- Provide migration examples
- Document new features

## 10. Success Criteria

### 10.1 Functional Success
- All NOC transfers complete correctly
- Resource allocation matches semantics
- Golden model validates all operations

### 10.2 Quality Metrics
- Zero resource allocation bugs
- 100% test coverage
- Clear separation of concerns