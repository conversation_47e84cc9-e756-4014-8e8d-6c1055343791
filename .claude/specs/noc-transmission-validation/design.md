# NoC Transmission Implementation Design

## 1. Overview

### 1.1 Purpose
This design document details the technical implementation for fixing NoC transmission issues in the PNM simulator, including primitive separation, resource allocation fixes, and golden model implementation.

### 1.2 Goals
- Separate NOC primitive into NOC_SRC and NOC_DEST types
- Fix resource allocation to match instruction semantics
- Implement actual data transfer in golden model
- Ensure proper synchronization between cores

### 1.3 Scope
Implementation changes across multiple modules: SIMTemplate.py, tensorinfo.py, NPUCore.py, Modules.py, and decoder.py.

## 2. Architecture Overview

```mermaid
graph TB
    subgraph "Instruction Flow"
        A[RISC-V Instructions] --> B[decoder.py]
        B --> C{Instruction Type}
        C -->|noc_src_drv| D[NOC_SRC Primitive]
        C -->|noc_dest_drv| E[NOC_DEST Primitive]
    end
    
    subgraph "SIMTop Pairing"
        D --> P[Pending List]
        E --> P
        P --> Q{Matching Pair?}
        Q -->|Yes| R[Execute Transfer]
        Q -->|No| S[Wait for Partner]
    end
    
    subgraph "Core Execution"
        R --> F[Source Core]
        R --> G[Destination Core]
        F -->|TX Router| H[NoC Network]
        H -->|RX Router| G
    end
    
    subgraph "Resource Allocation"
        F --> I[Source Memory + TX Only]
        G --> J[Dest Memory + RX Only]
    end
```

## 3. Component Design

### 3.1 Primitive Type Separation

#### 3.1.1 SIMTemplate.py Updates
```python
class PrimName(Enum):
    # Remove "Fake" comment and activate existing types
    NOC_FENCE = auto()  
    NOC_SRC = auto()    # Source core primitive
    NOC_DEST = auto()   # Destination core primitive
    NOC = auto()        # Keep for backward compatibility (deprecated)
```

#### 3.1.2 tensorinfo.py Updates
```python
convert_PrimName = {
    "noc_src_drv"   : PrimName.NOC_SRC,
    "noc_dest_drv"  : PrimName.NOC_DEST,
    "noc_fence_drv" : PrimName.NOC_FENCE,
}
```

### 3.2 Resource Allocation Fix

#### 3.2.1 NPUCore.py Resource Management
```python
def prim_module_use(self, prim):
    # Define separate primitive lists
    noc_src_list = [SIMTemp.PrimName.NOC_SRC]
    noc_dest_list = [SIMTemp.PrimName.NOC_DEST]
    noc_fence_list = [SIMTemp.PrimName.NOC_FENCE]
    
    # NOC_SRC: Only source core resources
    elif prim.type in noc_src_list:
        # Check if this core is the source (using group and id)
        if (self.group == prim.noc_settings.src_group and 
            self.id == prim.noc_settings.src_id):
            prim.module_use.append(self.noc_router_tx)
            prim.module_use.append(prim.noc_settings.src_memory)
            if isinstance(prim.noc_settings.src_memory, Mods.DRAMBank):
                prim.module_use.append(self.tensor_load_store_unit)
    
    # NOC_DEST: Only destination core resources
    elif prim.type in noc_dest_list:
        # Check if this core is the destination (using group and id)
        if (self.group == prim.noc_settings.dst_group and 
            self.id == prim.noc_settings.dst_id):
            prim.module_use.append(self.noc_router_rx)
            prim.module_use.append(prim.noc_settings.dst_memory)
            if isinstance(prim.noc_settings.dst_memory, Mods.DRAMBank):
                prim.module_use.append(self.tensor_load_store_unit)
    
    # NOC_FENCE: No resource allocation needed
    elif prim.type in noc_fence_list:
        pass  # Fence doesn't occupy resources
```

### 3.3 NoC Settings Enhancement

#### 3.3.1 SIMTemplate.py NoC Settings
```python
class NoCInfo:
    def __init__(self, parent_prim):
        # Existing fields
        self.src_addr = None
        self.dst_addr = None
        self.src_group = None
        self.src_id = None
        self.dst_group = None
        self.dst_id = None
        self.src_memory = None
        self.src_core = None
        self.dst_memory = None
        self.dst_core = None
        
        # New fields for transfer support
        self.transfer_size = 0
        self.transfer_complete = False
        self.data_buffer = None
        self.transfer_id = None         # For tracking transfers
        self.partner_primitive = None   # Link to paired primitive
        self.is_vector_operation = False  # Support for VNICE
```

### 3.4 Primitive Execution Logic

#### 3.4.1 NPUCore.py Execution Updates
```python
def prim_exe(self, prim):
    if prim.type == SIMTemp.PrimName.NOC_SRC:
        # Only source core executes NOC_SRC
        if (self.group == prim.noc_settings.src_group and 
            self.id == prim.noc_settings.src_id):
            # Read data from source memory
            data = self.read_memory_data(prim.noc_settings.src_memory, 
                                       prim.tensor_in1.byte_base,
                                       prim.noc_settings.transfer_size)
            # Store in NoC settings for transfer
            prim.noc_settings.data_buffer = data
            # Execute on TX router with NoC routing
            self.execute_noc_routing(prim)
            self.noc_router_tx.run_prim(prim)
    
    elif prim.type == SIMTemp.PrimName.NOC_DEST:
        # Only destination core executes NOC_DEST
        if (self.group == prim.noc_settings.dst_group and 
            self.id == prim.noc_settings.dst_id):
            # Execute on RX router
            self.noc_router_rx.run_prim(prim)
            # Get data from paired primitive
            if prim.noc_settings.partner_primitive:
                data = prim.noc_settings.partner_primitive.noc_settings.data_buffer
                if data is not None:
                    self.write_memory_data(prim.noc_settings.dst_memory,
                                         prim.tensor_out.byte_base,
                                         data)
                    prim.noc_settings.transfer_complete = True

def execute_noc_routing(self, prim):
    """Execute NoC routing for data transfer"""
    src_pos = [self.x, self.y]  # Current core position
    dst_pos = [prim.noc_settings.dst_core.x, prim.noc_settings.dst_core.y]
    
    # Request and establish route
    if self.top.noc.routing_request(src_pos, dst_pos):
        self.top.noc.routing(src_pos, dst_pos)
        # Route will be freed after transfer completes
        return True
    return False

def read_memory_data(self, memory, address, size):
    """Read data from memory module"""
    # Implementation depends on memory type (SRAM/DRAM)
    # This is a simplified version
    return memory.read(address, size)

def write_memory_data(self, memory, address, data):
    """Write data to memory module"""
    # Implementation depends on memory type (SRAM/DRAM)
    # This is a simplified version
    memory.write(address, data)
```

### 3.5 Golden Model Implementation

#### 3.5.1 SIMTemplate.py Golden Run (Simplified)
```python
def golden_run(self, core_id, golden_vm_system: sim_backend.GOLDEN_VM_SYSTEM):
    match self.type:
        case PrimName.NOC_SRC:
            # Read source data for validation
            desc_in = TensorInfo2Descriptor(self.tensor_in1)
            data = golden_vm_system.read_tensor(core_id, desc_in)
            # Store in primitive for transfer
            self.noc_settings.data_buffer = data
            return data
        
        case PrimName.NOC_DEST:
            # Write destination data for validation
            desc_out = TensorInfo2Descriptor(self.tensor_out)
            # Get data from paired primitive
            if self.noc_settings.partner_primitive:
                data = self.noc_settings.partner_primitive.noc_settings.data_buffer
                if data is not None:
                    golden_vm_system.write_tensor(core_id, desc_out, data)
                    return True
            return False
        
        case PrimName.NOC_FENCE:
            # Simple fence - no validation needed in golden model
            return True
        
        case PrimName.NOC:
            # Legacy support - will be deprecated
            raise NotImplementedError("Use NOC_SRC and NOC_DEST instead")
```

### 3.6 Synchronization Mechanism

#### 3.6.1 Primitive Pairing in SIMTop
```python
class PNMDie(SIMTop):
    def __init__(self, row, col, gldres):
        # Existing initialization...
        self.pending_noc_primitives = []  # Store unpaired NoC primitives
    
    def dispatch_primitive(self, primitive):
        """Modified dispatch to handle NoC primitive pairing"""
        if primitive.type in [SIMTemp.PrimName.NOC_SRC, SIMTemp.PrimName.NOC_DEST]:
            self.handle_noc_primitive(primitive)
        else:
            # Normal broadcast to all cores
            for i in range(self.col):
                for j in range(self.row):
                    self.npu_cores[i][j].prim_receive(dispatched_prim=primitive)
    
    def handle_noc_primitive(self, primitive):
        """Handle NoC primitive pairing before dispatch"""
        # Find matching primitive
        partner_idx = -1
        for idx, pending in enumerate(self.pending_noc_primitives):
            if self.is_matching_pair(primitive, pending):
                partner_idx = idx
                break
        
        if partner_idx >= 0:
            # Found pair, execute transfer
            partner = self.pending_noc_primitives.pop(partner_idx)
            self.execute_noc_transfer_pair(primitive, partner)
        else:
            # No pair found, add to pending list
            self.pending_noc_primitives.append(primitive)
    
    def is_matching_pair(self, prim1, prim2):
        """Check if two primitives form a valid NoC transfer pair"""
        return (prim1.noc_settings.src_core == prim2.noc_settings.src_core and
                prim1.noc_settings.dst_core == prim2.noc_settings.dst_core and
                prim1.type != prim2.type and
                {prim1.type, prim2.type} == {SIMTemp.PrimName.NOC_SRC, SIMTemp.PrimName.NOC_DEST})
    
    def execute_noc_transfer_pair(self, prim1, prim2):
        """Execute paired NoC transfer"""
        # Identify SRC and DEST primitives
        src_prim = prim1 if prim1.type == SIMTemp.PrimName.NOC_SRC else prim2
        dst_prim = prim1 if prim1.type == SIMTemp.PrimName.NOC_DEST else prim2
        
        # Link primitives
        src_prim.noc_settings.partner_primitive = dst_prim
        dst_prim.noc_settings.partner_primitive = src_prim
        
        # Dispatch to respective cores
        src_core = src_prim.noc_settings.src_core
        src_core.prim_receive(src_prim)
        
        dst_core = dst_prim.noc_settings.dst_core
        dst_core.prim_receive(dst_prim)
```

### 3.7 TensorInfo Updates

#### 3.7.1 tensorinfo.py Separate Handlers
```python
def fill_TensorInfo(prim, info_dict):
    """Updated to handle separate NOC primitive types"""
    prim_name = prim.name
    
    # Define primitive type groups
    noc_src = {PrimName.NOC_SRC}
    noc_dest = {PrimName.NOC_DEST}
    noc_fence = {PrimName.NOC_FENCE}
    
    if prim_name in noc_src:
        fill_TensorInfo_noc_src(prim, info_dict)
    elif prim_name in noc_dest:
        fill_TensorInfo_noc_dest(prim, info_dict)
    elif prim_name in noc_fence:
        # NOC_FENCE has no tensor info
        pass
    # ... other primitive types

def fill_TensorInfo_noc_src(prim, info_dict):
    """Fill tensor info for NOC_SRC primitive"""
    # Source tensor setup
    prim.tensor_in1.strides = info_dict.get('noc.strides', [0, 0, 0])
    prim.tensor_in1.shape = info_dict.get('noc.shape', [1, 1, 1])
    prim.tensor_in1.byte_base = info_dict['noc.base_addr']
    # Calculate transfer size
    prim.noc_settings.transfer_size = calculate_tensor_size(prim.tensor_in1)

def fill_TensorInfo_noc_dest(prim, info_dict):
    """Fill tensor info for NOC_DEST primitive"""
    # Destination tensor setup
    prim.tensor_out.strides = info_dict.get('noc.strides', [0, 0, 0])
    prim.tensor_out.shape = info_dict.get('noc.shape', [1, 1, 1])
    prim.tensor_out.byte_base = info_dict['noc.base_addr']
    # Calculate expected size
    prim.noc_settings.transfer_size = calculate_tensor_size(prim.tensor_out)
```

## 4. Implementation Flow

### 4.1 Instruction Sequence (per doc/noc.md)
1. **NOC_FENCE** - Ensure previous NoC operations complete (optional based on data dependencies)
2. **NOC Configuration** - Set transfer parameters for both source and destination
3. **NOC_DEST** - Destination core prepares to receive data
4. **NOC_SRC** - Source core initiates data transfer
5. **Data Transfer** - Actual memory copy via NoC network

### 4.1.1 Fence Synchronization
- NOC_FENCE is used when there are data dependencies between NoC operations
- When NPU Groups > 1, fence is sent sequentially to each group
- Each group waits for all cores to acknowledge before proceeding

### 4.2 Data Flow
```mermaid
sequenceDiagram
    participant RC as RISC-V Controller
    participant SC as Source Core
    participant DC as Destination Core
    participant NoC as NoC Network
    participant GM as Golden Model
    
    RC->>DC: noc_dest_drv (prepare)
    DC->>DC: Allocate RX resources
    DC->>GM: Track destination ready
    
    RC->>SC: noc_src_drv (send)
    SC->>SC: Read memory data
    SC->>GM: Track source data
    SC->>NoC: Transmit via TX
    
    NoC->>DC: Route data
    DC->>DC: Write memory data
    DC->>GM: Validate transfer
    
    RC->>SC: noc_fence_drv
    SC->>GM: Verify complete
```

## 5. Testing Strategy

### 5.1 Unit Tests
- Test NOC_SRC primitive resource allocation
- Test NOC_DEST primitive resource allocation
- Test golden model data tracking
- Test transfer completion validation

### 5.2 Integration Tests
- Single core-to-core transfer
- Multiple concurrent transfers
- Different memory types (SRAM/DRAM)
- Various data sizes

### 5.3 Validation Tests
- Golden model comparison
- Data integrity verification
- Cycle count accuracy
- Energy calculation correctness

## 6. Migration Plan

### 6.1 Backward Compatibility
- Keep NOC primitive type for transition with NotImplementedError
- Existing first_update_core mechanism remains for other uses
- Minimal changes to existing architecture

### 6.2 Phased Implementation
1. **Phase 1**: Activate NOC_SRC/NOC_DEST types in SIMTemplate.py
2. **Phase 2**: Update tensorinfo.py instruction mapping
3. **Phase 3**: Implement primitive pairing in SIMTop
4. **Phase 4**: Fix resource allocation in NPUCore.py
5. **Phase 5**: Integrate NoC routing and data transfer
6. **Phase 6**: Implement golden model validation
7. **Phase 7**: Comprehensive testing

## 7. Performance Considerations

### 7.1 Memory Efficiency
- Use lazy data copying where possible
- Implement zero-copy for same-memory transfers
- Cache frequently accessed data

### 7.2 Simulation Speed
- Minimize golden model overhead
- Batch small transfers
- Use efficient data structures

## 8. Error Handling

### 8.1 Resource Conflicts
- Detect and report resource allocation conflicts
- Provide clear error messages
- Suggest resolution strategies

### 8.2 Transfer Failures
- Track incomplete transfers
- Report at fence operations
- Provide debugging information

### 8.3 Pairing Failures
- Timeout mechanism for unpaired primitives
- Warning when NOC_SRC lacks NOC_DEST pair
- Clear error messages for mismatched transfers

## 9. Documentation Updates

### 9.1 Code Documentation
- Document new primitive types
- Explain resource allocation logic
- Provide usage examples

### 9.2 User Documentation
- Update NoC operation guide
- Provide migration examples
- Document new features

## 10. Success Criteria

### 10.1 Functional Success
- All NOC transfers complete correctly
- Resource allocation matches semantics
- Golden model validates all operations

### 10.2 Quality Metrics
- Zero resource allocation bugs
- 100% test coverage
- Clear separation of concerns