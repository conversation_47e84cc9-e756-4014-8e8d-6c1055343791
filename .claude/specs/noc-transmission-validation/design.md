# NoC Transmission Implementation Design

## 1. Overview

### 1.1 Purpose
This design document details the technical implementation for fixing NoC transmission issues in the PNM simulator, including primitive separation, resource allocation fixes, and golden model implementation.

### 1.2 Goals
- Separate NOC primitive into NOC_SRC and NOC_DEST types
- Fix resource allocation to match instruction semantics
- Implement actual data transfer in golden model
- Ensure proper synchronization between cores

### 1.3 Scope
Implementation changes across multiple modules: SIMTemplate.py, tensorinfo.py, NPUCore.py, Modules.py, and decoder.py.

## 2. Architecture Overview

```mermaid
graph TB
    subgraph "Instruction Flow"
        A[RISC-V Instructions] --> B[decoder.py]
        B --> C{Instruction Type}
        C -->|noc_src_drv| D[NOC_SRC Primitive]
        C -->|noc_dest_drv| E[NOC_DEST Primitive]
    end
    
    subgraph "SIMTop Pairing"
        D --> P[Pending List]
        E --> P
        P --> Q{Matching Pair?}
        Q -->|Yes| R[Execute Transfer]
        Q -->|No| S[Wait for Partner]
    end
    
    subgraph "Core Execution"
        R --> F[Source Core]
        R --> G[Destination Core]
        F -->|TX Router| H[NoC Network]
        H -->|RX Router| G
    end
    
    subgraph "Resource Allocation"
        F --> I[Source Memory + TX Only]
        G --> J[Dest Memory + RX Only]
    end
```

## 3. Component Design

### 3.1 Primitive Type Separation

#### 3.1.1 SIMTemplate.py Updates
```python
class PrimName(Enum):
    # Remove "Fake" comment and activate existing types
    NOC_FENCE = auto()  
    NOC_SRC = auto()    # Source core primitive
    NOC_DEST = auto()   # Destination core primitive
    NOC = auto()        # Keep for backward compatibility (deprecated)
```

#### 3.1.2 tensorinfo.py Updates
```python
convert_PrimName = {
    "noc_src_drv"   : PrimName.NOC_SRC,
    "noc_dest_drv"  : PrimName.NOC_DEST,
    "noc_fence_drv" : PrimName.NOC_FENCE,
}
```

### 3.2 Resource Allocation Fix

#### 3.2.1 NPUCore.py Resource Management
```python
def prim_module_use(self, prim):
    # Define separate primitive lists
    noc_src_list = [SIMTemp.PrimName.NOC_SRC]
    noc_dest_list = [SIMTemp.PrimName.NOC_DEST]
    noc_fence_list = [SIMTemp.PrimName.NOC_FENCE]
    
    # NOC_SRC: Only source core resources
    elif prim.type in noc_src_list:
        # Check if this core is the source (using group and id)
        if (self.group == prim.noc_settings.src_group and 
            self.id == prim.noc_settings.src_id):
            prim.module_use.append(self.noc_router_tx)
            prim.module_use.append(prim.noc_settings.src_memory)
            if isinstance(prim.noc_settings.src_memory, Mods.DRAMBank):
                prim.module_use.append(self.tensor_load_store_unit)
    
    # NOC_DEST: Only destination core resources
    elif prim.type in noc_dest_list:
        # Check if this core is the destination (using group and id)
        if (self.group == prim.noc_settings.dst_group and 
            self.id == prim.noc_settings.dst_id):
            prim.module_use.append(self.noc_router_rx)
            prim.module_use.append(prim.noc_settings.dst_memory)
            if isinstance(prim.noc_settings.dst_memory, Mods.DRAMBank):
                prim.module_use.append(self.tensor_load_store_unit)
    
    # NOC_FENCE: No resource allocation needed
    elif prim.type in noc_fence_list:
        pass  # Fence doesn't occupy resources
```

### 3.3 NoC Settings Enhancement

#### 3.3.1 SIMTemplate.py NoC Settings
```python
class NoCInfo:
    def __init__(self, parent_prim):
        # Existing fields
        self.src_addr = None
        self.dst_addr = None
        self.src_group = None
        self.src_id = None
        self.dst_group = None
        self.dst_id = None
        self.src_memory = None
        self.src_core = None
        self.dst_memory = None
        self.dst_core = None
        
        # New fields for transfer support
        self.transfer_size = 0          # Size in bytes
        self.transfer_complete = False  # Transfer completion flag
        self.data_buffer = None         # numpy array for data
        self.transfer_id = None         # Unique transfer ID
        self.partner_primitive = None   # Link to paired primitive
        self.is_vector_operation = False  # Support for VNICE
        self.route_established = False  # NoC route status
        self.dispatch_cycle = 0         # When primitive was dispatched
```

### 3.4 Primitive Execution Logic

#### 3.4.1 NPUCore.py Execution Updates
```python
def prim_exe(self, prim):
    # Store current primitive for routing cleanup
    self.current_primitive = prim
    
    if prim.type == SIMTemp.PrimName.NOC_SRC:
        # Only source core executes NOC_SRC
        if (self.group == prim.noc_settings.src_group and 
            self.id == prim.noc_settings.src_id):
            try:
                # Read data from source memory
                data = self.read_memory_data(prim.noc_settings.src_memory, 
                                           prim.tensor_in1.byte_base,
                                           prim.noc_settings.transfer_size)
                # Validate data size
                if data.nbytes != prim.noc_settings.transfer_size:
                    raise ValueError(f"Data size mismatch: {data.nbytes} != {prim.noc_settings.transfer_size}")
                    
                # Store in NoC settings for transfer
                prim.noc_settings.data_buffer = data
                
                # Execute on TX router with NoC routing
                self.execute_noc_routing(prim)
                self.noc_router_tx.run_prim(prim)
            except Exception as e:
                print(f"ERROR in NOC_SRC execution: {e}")
                raise
    
    elif prim.type == SIMTemp.PrimName.NOC_DEST:
        # Only destination core executes NOC_DEST
        if (self.group == prim.noc_settings.dst_group and 
            self.id == prim.noc_settings.dst_id):
            try:
                # Execute on RX router
                self.noc_router_rx.run_prim(prim)
                
                # Get data from paired primitive
                if prim.noc_settings.partner_primitive:
                    data = prim.noc_settings.partner_primitive.noc_settings.data_buffer
                    if data is not None:
                        # Validate data before writing
                        if data.nbytes != prim.noc_settings.transfer_size:
                            raise ValueError(f"Received data size mismatch: {data.nbytes} != {prim.noc_settings.transfer_size}")
                            
                        self.write_memory_data(prim.noc_settings.dst_memory,
                                             prim.tensor_out.byte_base,
                                             data)
                        prim.noc_settings.transfer_complete = True
                    else:
                        raise ValueError("No data buffer in partner primitive")
                else:
                    raise ValueError("No partner primitive linked")
            except Exception as e:
                print(f"ERROR in NOC_DEST execution: {e}")
                raise

def execute_noc_routing(self, prim):
    """Execute NoC routing for data transfer"""
    src_pos = [self.x, self.y]  # Current core position
    dst_pos = [prim.noc_settings.dst_core.x, prim.noc_settings.dst_core.y]
    
    # Request and establish route
    if self.top.noc.routing_request(src_pos, dst_pos):
        self.top.noc.routing(src_pos, dst_pos)
        prim.noc_settings.route_established = True
        return True
    else:
        # Routing failed - report error
        raise RuntimeError(f"NoC routing failed from Core[{src_pos}] to Core[{dst_pos}]")

def complete_noc_transfer(self, prim):
    """Complete NoC transfer and free routing resources"""
    if prim.noc_settings.route_established:
        src_pos = [self.x, self.y]
        dst_pos = [prim.noc_settings.dst_core.x, prim.noc_settings.dst_core.y]
        self.top.noc.routing_free(src_pos, dst_pos)
        prim.noc_settings.route_established = False

def read_memory_data(self, memory, address, size):
    """Read data from memory module"""
    # Calculate tensor dimensions from size
    # Assuming FP16 (2 bytes per element)
    num_elements = size // 2
    
    # Read from memory buffer
    offset = address - memory.address_space['Addr_Begin']
    data = np.zeros(num_elements, dtype=np.float16)
    
    for i in range(num_elements):
        byte_offset = offset + i * 2
        # Read 2 bytes and convert to FP16
        data[i] = memory.buffer[byte_offset:byte_offset+2].view(np.float16)[0]
    
    return data

def write_memory_data(self, memory, address, data):
    """Write data to memory module"""
    # Write to memory buffer
    offset = address - memory.address_space['Addr_Begin']
    
    # Ensure data is numpy array
    if not isinstance(data, np.ndarray):
        data = np.array(data, dtype=np.float16)
    
    # Write each element
    for i, value in enumerate(data):
        byte_offset = offset + i * 2
        memory.buffer[byte_offset:byte_offset+2] = value.tobytes()
    
    # Complete the transfer
    self.complete_noc_transfer(self.current_primitive)
```

### 3.5 Golden Model Implementation

#### 3.5.1 SIMTemplate.py Golden Run (Simplified)
```python
def golden_run(self, core_id, golden_vm_system: sim_backend.GOLDEN_VM_SYSTEM):
    match self.type:
        case PrimName.NOC_SRC:
            # Read source data for validation
            desc_in = TensorInfo2Descriptor(self.tensor_in1)
            data = golden_vm_system.read_tensor(core_id, desc_in)
            # Store in primitive for transfer
            self.noc_settings.data_buffer = data
            return data
        
        case PrimName.NOC_DEST:
            # Write destination data for validation
            desc_out = TensorInfo2Descriptor(self.tensor_out)
            # Get data from paired primitive
            if self.noc_settings.partner_primitive:
                data = self.noc_settings.partner_primitive.noc_settings.data_buffer
                if data is not None:
                    golden_vm_system.write_tensor(core_id, desc_out, data)
                    return True
            return False
        
        case PrimName.NOC_FENCE:
            # Simple fence - no validation needed in golden model
            return True
        
        case PrimName.NOC:
            # Legacy support - will be deprecated
            raise NotImplementedError("Use NOC_SRC and NOC_DEST instead")
```

### 3.6 Synchronization Mechanism

#### 3.6.1 Modified SIMTop Integration
```python
class PNMDie(SIMTop):
    def __init__(self, row, col, gldres):
        # Existing initialization...
        self.pending_noc_primitives = []  # Store unpaired NoC primitives
        self.noc_timeout_cycles = 1000    # Timeout for pairing
    
    def run_simulation_until(self, until_cycle):
        """Modified existing function to handle NoC pairing"""
        # ... existing code ...
        
        # Check for NoC timeouts periodically
        if cycle % 100 == 0:
            self.check_noc_timeouts(cycle)
        
        # ... existing code ...
        
        # MODIFIED: Replace existing dispatch logic
        if prim_dispatch.type == SIMTemp.PrimName.SYNC:
            self.synchronizing_npus = True
            self.synronize_prim_id = rv_dispatch.id
            self.synronize_prim_inst = rv_dispatch.inst
        elif prim_dispatch.type in [SIMTemp.PrimName.NOC_SRC, SIMTemp.PrimName.NOC_DEST]:
            # NEW: Handle NoC primitive pairing
            prim_dispatch.dispatch_cycle = cycle  # Track when dispatched
            self.handle_noc_primitive(prim_dispatch)
        else:
            # EXISTING: Broadcast to all cores
            for i in range(self.col):
                for j in range(self.row):
                    self.npu_cores[i][j].prim_receive(dispatched_prim=prim_dispatch)
    
    def handle_noc_primitive(self, primitive):
        """Handle NoC primitive pairing before dispatch"""
        # Find matching primitive
        partner_idx = -1
        for idx, pending in enumerate(self.pending_noc_primitives):
            if self.is_matching_pair(primitive, pending):
                partner_idx = idx
                break
        
        if partner_idx >= 0:
            # Found pair, execute transfer
            partner = self.pending_noc_primitives.pop(partner_idx)
            self.execute_noc_transfer_pair(primitive, partner)
        else:
            # No pair found, add to pending list
            self.pending_noc_primitives.append(primitive)
    
    def is_matching_pair(self, prim1, prim2):
        """Check if two primitives form a valid NoC transfer pair"""
        # Match based on source and destination cores
        return (prim1.noc_settings.src_group == prim2.noc_settings.src_group and
                prim1.noc_settings.src_id == prim2.noc_settings.src_id and
                prim1.noc_settings.dst_group == prim2.noc_settings.dst_group and
                prim1.noc_settings.dst_id == prim2.noc_settings.dst_id and
                prim1.type != prim2.type and
                {prim1.type, prim2.type} == {SIMTemp.PrimName.NOC_SRC, SIMTemp.PrimName.NOC_DEST})
    
    def execute_noc_transfer_pair(self, prim1, prim2):
        """Execute paired NoC transfer"""
        # Identify SRC and DEST primitives
        src_prim = prim1 if prim1.type == SIMTemp.PrimName.NOC_SRC else prim2
        dst_prim = prim1 if prim1.type == SIMTemp.PrimName.NOC_DEST else prim2
        
        # Link primitives
        src_prim.noc_settings.partner_primitive = dst_prim
        dst_prim.noc_settings.partner_primitive = src_prim
        
        # Dispatch to respective cores only
        src_core = src_prim.noc_settings.src_core
        src_core.prim_receive(src_prim)
        
        dst_core = dst_prim.noc_settings.dst_core
        dst_core.prim_receive(dst_prim)
    
    def check_noc_timeouts(self, current_cycle):
        """Check for NoC primitive pairing timeouts"""
        expired = []
        for prim in self.pending_noc_primitives:
            if current_cycle - prim.dispatch_cycle > self.noc_timeout_cycles:
                expired.append(prim)
        
        for prim in expired:
            self.pending_noc_primitives.remove(prim)
            print(f"ERROR: NoC primitive {prim.prim_id} ({prim.type}) timed out waiting for pair")
            print(f"  Source: Group {prim.noc_settings.src_group} ID {prim.noc_settings.src_id}")
            print(f"  Dest: Group {prim.noc_settings.dst_group} ID {prim.noc_settings.dst_id}")
```

### 3.7 TensorInfo Updates

#### 3.7.1 tensorinfo.py Separate Handlers
```python
def fill_TensorInfo(prim, info_dict):
    """Updated to handle separate NOC primitive types"""
    prim_name = prim.type  # Use type not name
    
    # Define primitive type groups
    noc_src = {PrimName.NOC_SRC}
    noc_dest = {PrimName.NOC_DEST}
    noc_fence = {PrimName.NOC_FENCE}
    
    if prim_name in noc_src:
        fill_TensorInfo_noc_src(prim, info_dict)
    elif prim_name in noc_dest:
        fill_TensorInfo_noc_dest(prim, info_dict)
    elif prim_name in noc_fence:
        # NOC_FENCE has no tensor info
        pass
    # ... other primitive types

def fill_TensorInfo_noc_src(prim, info_dict):
    """Fill tensor info for NOC_SRC primitive"""
    # Create tensor if not exists
    if prim.tensor_in1 is None:
        prim.tensor_in1 = TensorInfo()
    
    # Source tensor setup
    prim.tensor_in1.strides = info_dict.get('noc.strides', [0, 0, 0])
    prim.tensor_in1.shape = info_dict.get('noc.shape', [1, 1, 1])
    prim.tensor_in1.byte_base = info_dict['noc.base_addr']
    prim.tensor_in1.datatype = info_dict.get('noc.datatype', 'FP16')
    
    # Calculate transfer size in bytes
    elements = np.prod(prim.tensor_in1.shape)
    bytes_per_element = 2 if prim.tensor_in1.datatype == 'FP16' else 4
    prim.noc_settings.transfer_size = elements * bytes_per_element
    
    # Set memory reference
    prim.tensor_in1.memory = prim.noc_settings.src_memory

def fill_TensorInfo_noc_dest(prim, info_dict):
    """Fill tensor info for NOC_DEST primitive"""
    # Create tensor if not exists
    if prim.tensor_out is None:
        prim.tensor_out = TensorInfo()
    
    # Destination tensor setup
    prim.tensor_out.strides = info_dict.get('noc.strides', [0, 0, 0])
    prim.tensor_out.shape = info_dict.get('noc.shape', [1, 1, 1])
    prim.tensor_out.byte_base = info_dict['noc.base_addr']
    prim.tensor_out.datatype = info_dict.get('noc.datatype', 'FP16')
    
    # Calculate expected size in bytes
    elements = np.prod(prim.tensor_out.shape)
    bytes_per_element = 2 if prim.tensor_out.datatype == 'FP16' else 4
    prim.noc_settings.transfer_size = elements * bytes_per_element
    
    # Set memory reference
    prim.tensor_out.memory = prim.noc_settings.dst_memory
```

## 4. Implementation Flow

### 4.1 Instruction Sequence (per doc/noc.md)
1. **NOC_FENCE** - Ensure previous NoC operations complete (optional based on data dependencies)
2. **NOC Configuration** - Set transfer parameters for both source and destination
3. **NOC_DEST** - Destination core prepares to receive data
4. **NOC_SRC** - Source core initiates data transfer
5. **Data Transfer** - Actual memory copy via NoC network

### 4.1.1 Fence Synchronization
- NOC_FENCE is used when there are data dependencies between NoC operations
- When NPU Groups > 1, fence is sent sequentially to each group
- Each group waits for all cores to acknowledge before proceeding

### 4.1.2 Critical Implementation Notes
- **Primitive Dispatch**: Modified in `run_simulation_until`, not a new function
- **Resource Cleanup**: Routes freed after data write completes
- **Error Propagation**: All errors reported with context information
- **Memory Management**: Data buffers cleaned up after transfer

### 4.2 Data Flow
```mermaid
sequenceDiagram
    participant RC as RISC-V Controller
    participant ST as SIMTop
    participant SC as Source Core
    participant DC as Destination Core
    participant NoC as NoC Network
    participant GM as Golden Model
    
    RC->>ST: noc_dest_drv
    ST->>ST: Add to pending_noc_primitives
    
    RC->>ST: noc_src_drv
    ST->>ST: Find matching pair
    ST->>SC: Dispatch NOC_SRC
    ST->>DC: Dispatch NOC_DEST
    
    SC->>SC: Read memory data
    SC->>NoC: Request route
    SC->>GM: Track source data
    
    DC->>DC: Prepare to receive
    DC->>DC: Wait for data
    
    SC->>DC: Transfer via NoC
    DC->>DC: Write memory data
    DC->>NoC: Free route
    DC->>GM: Validate transfer
    
    RC->>ST: noc_fence_drv (if needed)
    ST->>GM: Verify all complete
```

## 5. Testing Strategy

### 5.1 Unit Tests
- Test NOC_SRC primitive resource allocation
- Test NOC_DEST primitive resource allocation
- Test golden model data tracking
- Test transfer completion validation

### 5.2 Integration Tests
- Single core-to-core transfer
- Multiple concurrent transfers
- Different memory types (SRAM/DRAM)
- Various data sizes

### 5.3 Validation Tests
- Golden model comparison
- Data integrity verification
- Cycle count accuracy
- Energy calculation correctness

## 6. Migration Plan

### 6.1 Backward Compatibility
- Keep NOC primitive type for transition with NotImplementedError
- Existing first_update_core mechanism remains for other uses
- Minimal changes to existing architecture

### 6.2 Phased Implementation
1. **Phase 1**: Activate NOC_SRC/NOC_DEST types in SIMTemplate.py
2. **Phase 2**: Update tensorinfo.py instruction mapping
3. **Phase 3**: Implement primitive pairing in SIMTop
4. **Phase 4**: Fix resource allocation in NPUCore.py
5. **Phase 5**: Integrate NoC routing and data transfer
6. **Phase 6**: Implement golden model validation
7. **Phase 7**: Comprehensive testing

## 7. Performance Considerations

### 7.1 Memory Efficiency
- Data buffer stored as numpy arrays for efficiency
- Maximum transfer size limited by NoC bandwidth
- Memory cleanup after transfer completion

### 7.2 Simulation Speed
- Periodic timeout checks (every 100 cycles)
- Direct primitive dispatch to target cores only
- Minimal golden model overhead

### 7.3 Large Data Transfers
- Transfer size calculated from tensor shape and datatype
- Support for both FP16 and FP32 data types
- Proper handling of memory alignment

## 8. Error Handling

### 8.1 Resource Conflicts
- Detect and report resource allocation conflicts
- Provide clear error messages
- Suggest resolution strategies

### 8.2 Transfer Failures
- Track incomplete transfers
- Report at fence operations
- Provide debugging information

### 8.3 Pairing Failures
- Timeout mechanism for unpaired primitives (1000 cycles default)
- Error reporting with source/destination details
- Clear identification of missing pair type

### 8.4 Data Integrity
- Validate data buffer size matches transfer size
- Check memory bounds before read/write
- Verify data type consistency

## 9. Documentation Updates

### 9.1 Code Documentation
- Document new primitive types
- Explain resource allocation logic
- Provide usage examples

### 9.2 User Documentation
- Update NoC operation guide
- Provide migration examples
- Document new features

## 10. Success Criteria

### 10.1 Functional Success
- All NOC transfers complete correctly
- Resource allocation matches semantics
- Golden model validates all operations
- No unpaired primitives after timeout

### 10.2 Quality Metrics
- Zero resource allocation bugs
- 100% test coverage
- Clear separation of concerns
- Proper error handling for all edge cases

### 10.3 Implementation Milestones
1. **Milestone 1**: Primitive types separated and mapping updated
2. **Milestone 2**: SIMTop pairing mechanism working
3. **Milestone 3**: Resource allocation fixed
4. **Milestone 4**: NoC routing integrated
5. **Milestone 5**: Data transfer functional
6. **Milestone 6**: Golden model validation complete
7. **Milestone 7**: All tests passing