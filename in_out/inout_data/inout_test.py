import numpy as np
import json
import sys


# * * * Tensor 数组的生成，存储和读取 BEGIN * * * #

# 随机生成一个 1×3×3 的 numpy 数组
arr = (np.random.rand(1, 3, 3)*16384).astype(np.int32)
print("随机生成的 numpy 数组：")
print(arr)

# 保存数组到文件
np.save('tensor1_0_0.npy', arr)
print("数组已保存到 tensor1_0_0.npy 文件中")

# 加载保存的 numpy 数组
loaded_arr = np.load('tensor1_0_0.npy')
print("从文件中读取的 numpy 数组：")
print(loaded_arr)

# * * * Tensor 数组的生成，存储和读取 END * * * #

# * * * JSON 文件的读取 BEGIN * * * #

# 读取 JSON 文件
file_path = 'output_tensor.json'
with open(file_path, 'r') as file:
    input_tensor = json.load(file)

# 打印整个 JSON 数据
print("整个 JSON 数据：")
print(input_tensor)

# 遍历数组中的每个对象
for tensor in input_tensor:
    print("\n处理一个 tensor：")
    print(f"名称: {tensor['name']}")
    print(f"地址: {tensor['address']}")
    print(f"维度: {tensor['dimension']}")
    print(f"步长: {tensor['stride']}")
    print(f"NumPy 文件名: {tensor['npFileName']}")

# * * * JSON 文件的读取 END * * * #

