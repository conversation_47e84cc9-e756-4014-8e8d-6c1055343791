# * * * 可视化 tensor，直接打印/显示存储状态 BEGIN * * * #

import numpy as np
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import tkinter as tk
from tkinter import ttk
import struct
import argparse
import sys

# # 创建一个 3 维 numpy 数组并保存
# # arr = np.random.randint(low=-256, high=255, size=(4, 32, 32), dtype=np.int16)  # 示例数组，dim2=10, dim1=100, dim0=100
# arr = np.random.randint(low=-16, high=15, size=(4, 40, 400), dtype=np.int8)  # 示例数组，dim2=10, dim1=100, dim0=100
# # 保存数组到文件
# np.save('test_tensor.npy', arr)
# print("数组已保存到 test_tensor.npy 文件中")

# * * * 输入 tensor 名称，读出对应 .npy 中的数据 BEGIN* * * #

# 创建 ArgumentParser 对象
parser = argparse.ArgumentParser(description="Tensor Visualize")

# 添加一个字符串类型的命令行参数
parser.add_argument("--tensor_name", type=str, help="Name of tensor to visualize")

# 解析命令行参数
args = parser.parse_args()

# 读出 npy 数据
npyFileName = args.tensor_name + '.npy'
array = np.load(npyFileName)
# print(array)

# * * * 输入 tensor 名称，读出对应 .npy 中的数据 END* * * #

# * * * 可视化 tensor，直接打印/显示存储状态 BEGIN * * * #

# 创建Tkinter窗口
root = tk.Tk()
root.title("3D Array Visualization")

# 创建一个包含滑块和输入框的框架
controls_frame = tk.Frame(root)
controls_frame.pack(side=tk.RIGHT, fill=tk.X, expand=0)

# 创建一个按钮，用于切换数据的显示格式
display_format = tk.StringVar(value="decimal")  # 默认显示十进制格式


def toggle_display_format():
    if display_format.get() == "decimal":
        display_format.set("binary")
    else:
        display_format.set("decimal")
    update_image()


# 将按钮放在controls_frame的底部
toggle_button = tk.Button(controls_frame, text="Decimal <--> Binary", command=toggle_display_format)
toggle_button.pack(side=tk.TOP, fill=tk.X, expand=1, pady=50)

# 创建matplotlib图形
fig, ax = plt.subplots(figsize=(20, 10))
canvas = FigureCanvasTkAgg(fig, master=root)
canvas.get_tk_widget().pack(side=tk.TOP, fill=tk.BOTH, expand=1)


def float16_to_binary(value):
    # 将 np.float16 转换为字节
    byte_value = struct.pack('>H', np.float16(value).view(np.uint16))
    # 将字节转换为整数
    int_value = struct.unpack('>H', byte_value)[0]
    # 将整数转换为二进制字符串
    binary_str = f"{int_value:016b}"
    return binary_str


def int16_to_binary(value):
    # 将 np.int16 转换为整数
    int_value = int(value)
    # 将整数转换为二进制字符串，补码形式
    binary_str = f"{int_value & 0xFFFF:016b}"
    return binary_str


def int8_to_binary(value):
    # 将 np.int8 转换为整数
    int_value = int(value)

    # 如果是负数，转换为补码形式
    if int_value < 0:
        int_value = (1 << 8) + int_value  # 256 + int_value

    # 将整数转换为 8 位二进制字符串
    binary_str = f"{int_value:08b}"

    return binary_str


# 更新图像的函数
def update_image(val=None):
    dim2_value = int(slider_dim2.get())  # 获取dim2的值
    start_dim1 = int(slider_dim1.get())  # 获取dim1的起始位置
    start_dim0 = int(slider_dim0.get())  # 获取dim0的起始位置

    # 确定显示的范围
    end_dim1 = min(start_dim1 + 16, array.shape[1])
    if array.dtype in [np.int16, np.float16]:
        end_dim0 = min(start_dim0 + 8, array.shape[2])
    elif array.dtype == np.int8:
        end_dim0 = min(start_dim0 + 16, array.shape[2])

    # 清除当前图像
    ax.clear()
    # 设置背景为白色
    ax.set_facecolor('white')

    # 在每个格点上显示数值
    for i in range(start_dim1, end_dim1):
        for j in range(start_dim0, end_dim0):
            value = array[dim2_value, i, j]
            if display_format.get() == "binary":
                # 显示二进制格式
                if array.dtype == np.float16:
                    text_value = f"{float16_to_binary(value)}"
                elif array.dtype == np.int16:
                    text_value = f"{int16_to_binary(value)}"
                elif array.dtype == np.int8:
                    text_value = f"{int8_to_binary(value)}"
            else:
                # 显示十进制格式
                if array.dtype == np.float16:
                    text_value = f"{value:.2f}"
                elif array.dtype == np.int16:
                    text_value = f"{value:.0f}"
                elif array.dtype == np.int8:
                    text_value = f"{value:.0f}"
            ax.text(j - start_dim0, i - start_dim1, text_value, ha="center", va="center", color="black")

    # 用灰色的线段将格子分隔开
    for i in range(end_dim1 - start_dim1 + 1):
        ax.axhline(i - 0.5, color='gray', linewidth=0.5)
    for j in range(end_dim0 - start_dim0 + 1):
        ax.axvline(j - 0.5, color='gray', linewidth=0.5)

    # 设置坐标轴范围
    ax.set_xlim(-0.5, end_dim0 - start_dim0 - 0.5)
    ax.set_ylim(-0.5, end_dim1 - start_dim1 - 0.5)

    # 设置坐标轴刻度，对应到dim1和dim0的实际值
    ax.set_xticks(np.arange(end_dim0 - start_dim0))
    ax.set_yticks(np.arange(end_dim1 - start_dim1))
    ax.set_xticklabels(np.arange(start_dim0, end_dim0))
    ax.set_yticklabels(np.arange(start_dim1, end_dim1))

    ax.set_title(f"dim2 = {dim2_value}, dim1 start = {start_dim1}, dim0 start = {start_dim0}")

    canvas.draw()  # 重新绘制图像


# 创建滑块和输入框
def update_slider_from_entry(entry, slider):
    try:
        value = int(entry.get())
        slider.set(value)
        update_image()
    except ValueError:
        pass


# 创建dim2滑块和输入框
dim2_frame = tk.Frame(controls_frame)
dim2_frame.pack(side=tk.TOP, fill=tk.X, expand=1)
tk.Label(dim2_frame, text=f"dim2 (0 to {array.shape[0] - 1})").pack(side=tk.LEFT)
slider_dim2 = ttk.Scale(dim2_frame, from_=0, to=array.shape[0] - 1, orient='horizontal', command=update_image)
slider_dim2.pack(side=tk.LEFT, fill=tk.X, expand=1)
entry_dim2 = tk.Entry(dim2_frame, width=5)
entry_dim2.pack(side=tk.LEFT)
entry_dim2.bind("<Return>", lambda event: update_slider_from_entry(entry_dim2, slider_dim2))
entry_dim2.insert(0, '0')

# 创建dim1滑块和输入框
dim1_frame = tk.Frame(controls_frame)
dim1_frame.pack(side=tk.TOP, fill=tk.X, expand=1)
tk.Label(dim1_frame, text=f"dim1 (0 to {array.shape[1] - 1})").pack(side=tk.LEFT)
slider_dim1 = ttk.Scale(dim1_frame, from_=0, to=array.shape[1] - 1, orient='horizontal', command=update_image)
slider_dim1.pack(side=tk.LEFT, fill=tk.X, expand=1)
entry_dim1 = tk.Entry(dim1_frame, width=5)
entry_dim1.pack(side=tk.LEFT)
entry_dim1.bind("<Return>", lambda event: update_slider_from_entry(entry_dim1, slider_dim1))
entry_dim1.insert(0, '0')

# 创建dim0滑块和输入框
dim0_frame = tk.Frame(controls_frame)
dim0_frame.pack(side=tk.TOP, fill=tk.X, expand=1)
tk.Label(dim0_frame, text=f"dim0 (0 to {array.shape[2] - 1})").pack(side=tk.LEFT)
slider_dim0 = ttk.Scale(dim0_frame, from_=0, to=array.shape[2] - 1, orient='horizontal', command=update_image)
slider_dim0.pack(side=tk.LEFT, fill=tk.X, expand=1)
entry_dim0 = tk.Entry(dim0_frame, width=5)
entry_dim0.pack(side=tk.LEFT)
entry_dim0.bind("<Return>", lambda event: update_slider_from_entry(entry_dim0, slider_dim0))
entry_dim0.insert(0, '0')

# 初始化图像
update_image()


def on_closing():
    print("Window is closing")
    root.destroy()
    sys.exit()


# 捕获窗口关闭事件
root.protocol("WM_DELETE_WINDOW", on_closing)

# 运行Tkinter主循环
root.mainloop()

# * * * 可视化 tensor，直接打印/显示存储状态 END * * * #
