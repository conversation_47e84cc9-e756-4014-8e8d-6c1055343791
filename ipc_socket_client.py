import socket
import os
import time

from ipc_message import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, IPC_RESPONSE_HEADER_LENGTH

class IpcSocketClient:
    def __init__(self, name: str):
        self.name = name

        self.connected = False

        # Create the Unix socket client
        self.client = socket.socket(socket.AF_UNIX, socket.SOCK_STREAM)
    
    def destroy(self):
        try:
            self.client.close()
        except Exception as e:
            print(f"IpcSocketClient close client fail! {e}")

    def connecting(self):
        # Connect to the server
        while True:
            try:
                self.client.connect(self.name)
                self.connected = True
                return
            except Exception as e:
                print(f"IpcSocketClient connecting server fail! server: {self.name} error: {e}")
                time.sleep(3)

    def request(self, req):
        if not self.connected:
            self.connecting()

        try:
            req_headers = req.header_to_bytes()
            self.client.sendall(req_headers)
            print(f"ipcSocketClient send request header: {req_headers.hex()}")
            if req.length > 0:
                self.client.sendall(req.data)
                print(f"ipcSocketClient send request data: {req.data.hex()}")

            # Receive a response from the server
            resp_headers = self.client.recv(IPC_RESPONSE_HEADER_LENGTH)
            resp = IpcResponse()
            resp.header_from_bytes(resp_headers)

            if resp.length > 0:
                resp.data = self.client.recv(resp.length)

            return resp
        except Exception as e:
            print(f"IpcSocketClient send request fail! server: {self.name}, error: {e}")

            try:
                self.client.close()
            except Exception as e:
                pass

            self.connected = False
              