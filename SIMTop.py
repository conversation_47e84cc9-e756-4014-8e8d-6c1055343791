# * * * * * * * * * * * * * * * * #
# Describe Top Level Connection and Run Top Level Simulation
# Author: <PERSON><PERSON><PERSON>
# * * * * * * * * * * * * * * * * #

import NoC
import NPUCore
import SIMTemplate as SIMTemp
import SIMInput as SIMIn
import GlobalSettings as GlbSet
import GoldenModel as GldMod
import Visualize as Vis
import Utility as Utils

import time
import json
import numpy as np
import os
import warnings
import sys
from collections import namedtuple

from sim_backend_torch import GOLDEN_VM_SYSTEM

INST_RSP_TYPE = namedtuple("RSP_TYPE", ["rsp_time", "id", "type", "ret", "xd"]) # type "nice"/"vnice"


class PNMDie:
    def __init__(self, name, req_queue, rsp_queue, nice_interface, row, col, debug=False, gldres=False):
        # golden virtual machine
        self.golden_vm_system = GOLDEN_VM_SYSTEM()

        self.row = row
        self.col = col
        self.noc = NoC.Mesh2D(row=row, col=col)
        self.npu_cores = [[NPUCore.NPUCore(x=i, y=j, top=self, gldres=gldres, golden_vm_system=self.golden_vm_system) for j in range(row)] for i in range(col)]
        # 独立运行版本才需要该属性，与 Mosim 联合时 Simulator 完全被动，需要动态接收新 Primitive
        # self.riscv_prims = SIMTemp.Queue(queue_name='RVPrims')
        self.debug = debug
        self.group_id_init()
        # 独立运行版本才需要该函数，与 Mosim 联合时 Simulator 完全被动，需要动态接收新 Primitive
        # self.prims_init()
        self.max_sim_cycle = 10e9
        self.max_prim_num = 10e6
        self.total_sim_cycles = None
        self.core_monitoring()
        self.gldres = gldres

        # Modification for joint simulation with Mosim
        # Mosimrequires the simulation to be performed up to this cycle.
        self.target_sim_cycle = -1
        # Below are states for PNMOp_simulator
        # 仿真是否结束，其标准为：
        # 1) risc-v所有 primitive 均发送完毕（包括本 cycle 也未发送新 primitive）
        # 2) 所有 npu cores 的所有指令均运行完毕
        self.simulation_end = False
        # 当前仿真 cycle, 仿真 prim 数, 下一个仿真 cycle
        self.current_cycle = 0
        self.num_dispatched_prim = 0
        self.next_cycle = 1
        # 是否正在同步，若是，则 risc-v 不会继续发送新的 primitive，直到所有 npu cores 的所有指令都运行完毕
        self.synchronizing_npus = False
        # 仿真时间记录
        self.simulation_wallclock = 0

        # interface for MoSIM
        self.name = name
        self.req_queue = req_queue
        self.rsp_queue = rsp_queue
        self.nice_interface = nice_interface
        self.rv_end_simulation = False
        self.synronize_prim_id = None
        self.synronize_prim_inst = None

        # Return value tracking for instructions
        self.pending_instructions = {}  # {inst_id: {'type': 'nice'/'vnice', 'cores': set(), 'returns': {}, 'cycle': int, 'inst': int}}
        self.next_inst_id = 0



    def group_id_init(self):
        assert self.col * self.row == GlbSet.NPU_Num_per_Group * GlbSet.GROUP_Num_per_Die
        # initialize id, and map id to group
        for i in range(self.col):
            for j in range(self.row):
                group = (i + j * self.col) // GlbSet.NPU_Num_per_Group
                id = i + j * self.col - group * GlbSet.NPU_Num_per_Group
                self.npu_cores[i][j].group = group
                self.npu_cores[i][j].id = id
                # self.npu_cores[i][j].global_id = i * self.row + j
                self.npu_cores[i][j].global_id = j * self.col + i
                # print(self.npu_cores[i][j])

    def find_core_group_id(self, group, id):
        # Index to the NPU core based on the values of group and id.
        i = (group * GlbSet.NPU_Num_per_Group + id) % self.col
        j = (group * GlbSet.NPU_Num_per_Group + id) // self.col
        return self.npu_cores[i][j]

    def print_group_mask(self):
        for cur_group in range(GlbSet.GROUP_Num_per_Die):
            print('Group:{} - Mask:[{}, {}, {}, {}]'.format(cur_group, 
            self.find_core_group_id(cur_group, 0).flag_receive_prim, 
            self.find_core_group_id(cur_group, 1).flag_receive_prim, 
            self.find_core_group_id(cur_group, 2).flag_receive_prim, 
            self.find_core_group_id(cur_group, 3).flag_receive_prim))

    def prims_init(self):
        SIMIn.prims_init(riscv_prims=self.riscv_prims)
        # Add a SYNC instruction at the end
        last_sync = SIMTemp.Primitive()
        last_sync.type = SIMTemp.PrimName.SYNC
        self.riscv_prims.bottom().primitives.push(last_sync)

    def core_monitoring(self):
        # Monitoring NPU
        # group 0
        self.npu_cores[0][0].monitoring = True
        self.npu_cores[1][0].monitoring = True
        self.npu_cores[2][0].monitoring = True
        self.npu_cores[3][0].monitoring = True
        # # group 1
        self.npu_cores[4][0].monitoring = True
        self.npu_cores[5][0].monitoring = True
        self.npu_cores[6][0].monitoring = True
        self.npu_cores[7][0].monitoring = True
        # # group 2
        self.npu_cores[0][1].monitoring = True
        self.npu_cores[1][1].monitoring = True
        self.npu_cores[2][1].monitoring = True
        self.npu_cores[3][1].monitoring = True
        # # group 3
        self.npu_cores[4][1].monitoring = True
        self.npu_cores[5][1].monitoring = True
        self.npu_cores[6][1].monitoring = True
        self.npu_cores[7][1].monitoring = True

    def run_simulation_until(self):
        # In order to meet the joint simulation requirements with Mosim,
        # the following modifications are made to the new version:
        # 1. The simulator is a completely passive simulation module.
        # 2. It accepts requests for the cycle to which the simulation needs to be performed each time
        # and then simulates up to that cycle.

        print('* * * * Request Simulate to {} * * * *'.format(self.target_sim_cycle))
        print('How many requests in queue? - {}'.format(self.req_queue.qsize()))
        self.simulation_end = False

        start_time = time.time()
        while not self.simulation_end:

            # All NPU Banks Update
            for i in range(self.col):
                for j in range(self.row):
                    self.npu_cores[i][j].update(simulation_cycle=self.current_cycle)

            # Collect return values from cores
            self.collect_return_values()

            # All NPU Stalls or Not
            break_flag = False
            all_npu_stall = True
            for i in range(self.col):
                for j in range(self.row):
                    if not self.npu_cores[i][j].whether_stall():
                        all_npu_stall = False
                        break_flag = True
                        break
                if break_flag:
                    break

            # 如果此前在同步npu, 然后现在完成了同步，则返回 1 给 rv
            if self.synchronizing_npus and all_npu_stall:
                inst_rsp = INST_RSP_TYPE(
                    rsp_time=self.current_cycle, 
                    id=self.synronize_prim_id,
                    type="nice",
                    ret=0x00000001,  # random return value
                    xd=(self.synronize_prim_inst & 0x4000) > 0  # xd bit
                )
                self.rsp_queue.put(inst_rsp)

            self.synchronizing_npus = self.synchronizing_npus and (not all_npu_stall)

            # Risc-V Dispatched from Mosim
            # · If not synchronizing, find the required dispatchable primitive.
            # 对于 op，先 top，如果是最后一个 prim，则 pop；对于 prim，直接 pop
            prim_dispatch = None

            if (not self.synchronizing_npus and (not self.req_queue.empty()) and
                    self.num_dispatched_prim != self.max_prim_num and
                    self.current_cycle >= self.req_queue.peek().req_time):
                rv_dispatch = self.req_queue.get()
                # self.instruction_logfile.write(f"[{self.current_cycle}] receive inst {rv_dispatch.inst:08x}, op1={rv_dispatch.op1}, op2={rv_dispatch.op2}\n")
                # self.instruction_logfile.flush()

                is_drv, primitive, info_dict = self.nice_interface.decoder(rv_dispatch.id, rv_dispatch.inst,
                                                                           rv_dispatch.op1, rv_dispatch.op2,
                                                                           self.current_cycle
                                                                           )
                # return either True, sim_prim, primitive_info; or False, None, None
                # The sim_prim should be consistent with the original SIMTemplate,
                # and primitive_info is a corresponding dictionary mainly used by Mosim.
                    

                if is_drv:
                    # self.primitive_logfile.write(f"[{self.current_cycle}] Create PrimitiveInfo {json.dumps(info_dict)}\n")
                    # self.primitive_logfile.flush()

                    primitive.prim_id = self.num_dispatched_prim

                    # Add instruction tracking for return value handling
                    needs_return_value = (rv_dispatch.inst & 0x4000) > 0
                    if needs_return_value:
                        inst_type = "vnice" if (rv_dispatch.inst & 0x7f) == 0x2B else "nice"
                        inst_id = self.next_inst_id
                        self.next_inst_id += 1

                        # Add instruction metadata to primitive
                        primitive.inst_id = inst_id
                        primitive.inst_type = inst_type
                        primitive.original_inst = rv_dispatch.inst

                        # Track which cores should execute this instruction
                        active_cores = set()
                        for i in range(self.col):
                            for j in range(self.row):
                                core = self.npu_cores[i][j]
                                # Check if core is in the target group and enabled by mask
                                if core.group == primitive.npu_group and (primitive.npu_mask & (1 << core.id)) != 0:
                                    active_cores.add(core.global_id)

                        self.pending_instructions[inst_id] = {
                            'type': inst_type,
                            'cores': active_cores,
                            'returns': {},
                            'cycle': self.current_cycle,
                            'inst': rv_dispatch.inst,
                            'rv_id': rv_dispatch.id
                        }

                    prim_dispatch = primitive
                    print('Verifying, current_cycle:{} - sychronizing_npus:{} - target_sim_cycle:{}'.format(self.current_cycle, self.synchronizing_npus, self.target_sim_cycle))
                    self.print_group_mask()
                    print('Verifying primitive: {}'.format(primitive))
                    print('Verifying Tensor_in1: {}'.format(primitive.tensor_in1))
                    print('Verifying Tensor_in2: {}'.format(primitive.tensor_in2))
                    print('Verifying Tensor_out: {}'.format(primitive.tensor_out))
                    print('Verifying conv_settings: {}'.format(primitive.conv_settings))

                    if prim_dispatch.type == SIMTemp.PrimName.SYNC:
                        self.synchronizing_npus = True
                        self.synronize_prim_id = rv_dispatch.id
                        self.synronize_prim_inst = rv_dispatch.inst
                    else:
                        # · Dispatch primitive to all NPU Cores.
                        # (Based on the group and mask information,
                        # the NPU core will determine whether to mask the current primitive.)
                        for i in range(self.col):
                            for j in range(self.row):
                                self.npu_cores[i][j].prim_receive(dispatched_prim=prim_dispatch)
                else:
                    print('ddd, is_drv=False')

            if prim_dispatch is not None:
                self.num_dispatched_prim += 1

            # Find Next Cycle to Simulate
            if not self.synchronizing_npus:
                if self.req_queue.empty():  # if No primitive in queue
                    self.next_cycle = self.current_cycle + 1e10
                    for i in range(self.col):
                        for j in range(self.row):
                            self.next_cycle = min(self.next_cycle, self.npu_cores[i][j].next_cycle)
                            # print(i, j, self.npu_cores[i][j].next_cycle)
                    if self.next_cycle == self.current_cycle + 1e10:
                        self.next_cycle = self.target_sim_cycle
                else:                       # if stills has primitive in queue
                    self.next_cycle = self.current_cycle + 1
            else:       # if synchronizing npus
                self.next_cycle = self.current_cycle + 1e10
                for i in range(self.col):
                    for j in range(self.row):
                        self.next_cycle = min(self.next_cycle, self.npu_cores[i][j].next_cycle)

            if self.debug:
                print('cur_cycle:', self.current_cycle, ', next_cycle:', self.next_cycle)
            if self.debug and prim_dispatch:
                print('dispatching:', prim_dispatch)

            if self.next_cycle > self.target_sim_cycle:
                self.current_cycle = self.target_sim_cycle
            else:
                self.current_cycle = self.next_cycle

            # Simulation End or Not
            sim_end = self.req_queue.empty() and (prim_dispatch is None) and all_npu_stall
            self.simulation_end = sim_end or (self.num_dispatched_prim == self.max_prim_num and all_npu_stall)

            if self.current_cycle == self.max_sim_cycle:
                self.simulation_end = True

            if self.simulation_end and self.rv_end_simulation:
                end_time = time.time()
                self.simulation_wallclock += end_time - start_time
                self.total_sim_cycles = self.current_cycle
                self.npu_cores_record_finished_prim()
                if self.current_cycle > 1e10:
                    end_cycle = self.current_cycle - 1e10
                else:
                    end_cycle = self.current_cycle
                end_cycle = Utils.float_to_int32(end_cycle)
                print('Simulation Ends @', end_cycle, 'cycle')
                print(f"Simulation Time Cost: {self.simulation_wallclock:.2f} sec")
                self.data_export()
                # sys.exit()

            # simulation until
            if self.current_cycle >= self.target_sim_cycle:
                break

        end_time = time.time()
        self.simulation_wallclock += end_time - start_time
        

    def run_simulation_until_beta_version(self):
        # In order to meet the joint simulation requirements with Mosim,
        # the following modifications are made to the new version:
        # 1. The simulator is a completely passive simulation module.
        # 2. It accepts requests for the cycle to which the simulation needs to be performed each time
        # and then simulates up to that cycle.

        start_time = time.time()
        while not self.simulation_end:

            # All NPU Banks Update
            for i in range(self.col):
                for j in range(self.row):
                    self.npu_cores[i][j].update(simulation_cycle=self.current_cycle)

            # All NPU Stalls or Not
            break_flag = False
            all_npu_stall = True
            for i in range(self.col):
                for j in range(self.row):
                    if not self.npu_cores[i][j].whether_stall():
                        all_npu_stall = False
                        break_flag = True
                        break
                if break_flag:
                    break
            self.synchronizing_npus = self.synchronizing_npus and (not all_npu_stall)

            # Risc-V Dispatch
            # · If not synchronizing, find the required dispatchable primitive.
            # 对于 op，先 top，如果是最后一个 prim，则 pop；对于 prim，直接 pop
            prim_dispatch = None
            if (not self.synchronizing_npus and self.riscv_prims.not_empty() and
                    self.num_dispatched_prim != self.max_prim_num):
                if self.riscv_prims.top().primitives.length() == 1:
                    op_dispatch = self.riscv_prims.pop()
                else:
                    op_dispatch = self.riscv_prims.top()
                prim_dispatch = op_dispatch.primitives.pop()

                if prim_dispatch.type == SIMTemp.PrimName.SYNC:
                    self.synchronizing_npus = True
                else:
                    # · Dispatch primitive to all NPU Cores.
                    # (Based on the group and mask information,
                    # the NPU core will determine whether to mask the current primitive.)
                    for i in range(self.col):
                        for j in range(self.row):
                            self.npu_cores[i][j].prim_receive(dispatched_prim=prim_dispatch)
            if prim_dispatch is not None:
                self.num_dispatched_prim += 1

            # Find Next Cycle to Simulate
            if not self.synchronizing_npus:
                self.next_cycle = self.current_cycle + 1
            else:
                self.next_cycle = self.current_cycle + 1e10
                for i in range(self.col):
                    for j in range(self.row):
                        self.next_cycle = min(self.next_cycle, self.npu_cores[i][j].next_cycle)

            if self.debug:
                print('cur_cycle:', self.current_cycle, ', next_cycle:', self.next_cycle)
            if self.debug and prim_dispatch:
                print('dispatching:', prim_dispatch)

            self.current_cycle = self.next_cycle

            # Simulation End or Not
            sim_end = self.riscv_prims.is_empty() and (prim_dispatch is None) and all_npu_stall
            self.simulation_end = sim_end or (self.num_dispatched_prim == self.max_prim_num and all_npu_stall)
            if self.current_cycle == self.max_sim_cycle:
                self.simulation_end = True

            if self.simulation_end:
                end_time = time.time()
                self.simulation_wallclock += end_time - start_time
                self.total_sim_cycles = self.current_cycle
                self.npu_cores_record_finished_prim()
                print('Simulation Ends @', self.current_cycle, 'cycle')
                print(f"Simulation Time Cost: {self.simulation_wallclock:.2f} sec")

            # simulation until
            if self.current_cycle >= self.target_sim_cycle:
                break

        end_time = time.time()
        self.simulation_wallclock += end_time - start_time

    def run_simulation_v0p4(self):

        # 仿真是否结束，其标准为：
        # 1) risc-v所有 primitive 均发送完毕（包括本 cycle 也未发送新 primitive）
        # 2) 所有 npu cores 的所有指令均运行完毕
        simulation_end = False

        # 是否所有 npu cores 的所有指令均运行完毕
        all_npu_stall = True

        # 当前仿真 cycle 和 仿真 prim 数
        current_cycle = 0
        num_dispatched_prim = 0

        # 是否正在同步，若是，则 risc-v 不会继续发送新的 primitive，直到所有 npu cores 的所有指令都运行完毕
        synchronizing_npus = False

        start_time = time.time()
        while not simulation_end:

            # All NPU Banks Update
            for i in range(self.col):
                for j in range(self.row):
                    self.npu_cores[i][j].update(simulation_cycle=current_cycle)

            # All NPU Stalls or Not
            break_flag = False
            all_npu_stall = True
            for i in range(self.col):
                for j in range(self.row):
                    if not self.npu_cores[i][j].whether_stall():
                        all_npu_stall = False
                        break_flag = True
                        break
                if break_flag:
                    break
            synchronizing_npus = synchronizing_npus and (not all_npu_stall)

            # Risc-V Dispatch
            # · If not synchronizing, find the required dispatchable primitive.
            # 对于 op，先 top，如果是最后一个 prim，则 pop；对于 prim，直接 pop
            prim_dispatch = None
            if not synchronizing_npus and self.riscv_prims.not_empty() and num_dispatched_prim != self.max_prim_num:
                if self.riscv_prims.top().primitives.length() == 1:
                    op_dispatch = self.riscv_prims.pop()
                else:
                    op_dispatch = self.riscv_prims.top()
                prim_dispatch = op_dispatch.primitives.pop()

                if prim_dispatch.type == SIMTemp.PrimName.SYNC:
                    synchronizing_npus = True
                else:
                    # · Dispatch primitive to all NPU Cores.
                    # (Based on the group and mask information,
                    # the NPU core will determine whether to mask the current primitive.)
                    for i in range(self.col):
                        for j in range(self.row):
                            self.npu_cores[i][j].prim_receive(dispatched_prim=prim_dispatch)
            if prim_dispatch is not None:
                num_dispatched_prim += 1

            # Fine Next Cycle to Simulate
            if not synchronizing_npus:
                next_cycle = current_cycle + 1
            else:
                next_cycle = current_cycle + 1e10
                for i in range(self.col):
                    for j in range(self.row):
                        next_cycle = min(next_cycle, self.npu_cores[i][j].next_cycle)

            if self.debug:
                print('cur_cycle:', current_cycle, ', next_cycle:', next_cycle)
            if self.debug and prim_dispatch:
                print('dispatching:', prim_dispatch)

            current_cycle = next_cycle

            # Simulation End or Not
            simulation_end = self.riscv_prims.is_empty() and (prim_dispatch is None) and all_npu_stall
            simulation_end = simulation_end or (num_dispatched_prim == self.max_prim_num and all_npu_stall)
            if current_cycle == self.max_sim_cycle:
                simulation_end = True

            if simulation_end:
                print('Simulation Ends @', current_cycle, 'cycle')
                end_time = time.time()
                elapsed_time = end_time - start_time
                print(f"Simulation Time Cost: {elapsed_time:.2f} sec")

        self.total_sim_cycles = current_cycle

        self.npu_cores_record_finished_prim()

    def npu_cores_record_finished_prim(self):
        for i in range(self.col):
            for j in range(self.row):
                self.npu_cores[i][j].record_finished_prim()

    def data_import(self):
        inout_path = './in_out/inout_data/'
        in_data_path = inout_path + 'in_data/'

        self.golden_vm_system.data_import(
            json_file_path=inout_path + "input_tensor.json",
            data_dir=in_data_path
        )

    def data_export(self):
        inout_path = './in_out/inout_data/'
        out_data_path = inout_path + 'out_data/'

        self.golden_vm_system.data_export(
            json_file_path=inout_path + "output_tensor.json",
            data_dir=out_data_path
        )

    # interface for Mosim
    def execute_until(self, end_time):
        # if end_time == self.current_cycle:
        #     warnings.warn('Wrong end_time in func: execute_until! end_time:{} = self.current_cycle:{}'.format(end_time, self.current_cycle))
        if end_time < self.current_cycle:
            raise Exception('Wrong end_time in func: execute_until! end_time:{} = self.current_cycle:{}'.format(end_time, self.current_cycle))
        self.target_sim_cycle = end_time
        self.run_simulation_until()

    def get_check_cycle(self):
        return Utils.float_to_int32(self.next_cycle)

    def end_simulation(self):
        self.rv_end_simulation = True
        self.target_sim_cycle += self.max_sim_cycle
        self.run_simulation_until()

        self.golden_vm_system.save_log("logs/golden_vm.log")

    def collect_return_values(self):
        """
        Collect return values from all cores and generate responses for completed instructions.
        """
        # Collect return values from all cores
        for i in range(self.col):
            for j in range(self.row):
                core = self.npu_cores[i][j]
                core_returns = core.get_and_clear_return_values()

                for prim_id, return_info in core_returns.items():
                    inst_id = return_info['inst_id']
                    if inst_id in self.pending_instructions:
                        pending_inst = self.pending_instructions[inst_id]
                        pending_inst['returns'][return_info['core_id']] = return_info['ret']

                        # Check if all expected cores have returned values
                        if len(pending_inst['returns']) == len(pending_inst['cores']):
                            # All cores completed, generate response
                            self.generate_instruction_response(inst_id, pending_inst)
                            del self.pending_instructions[inst_id]

    def generate_instruction_response(self, inst_id, pending_inst):
        """
        Generate and queue the response for a completed instruction.
        """
        if pending_inst['type'] == 'nice':
            # For NICE instructions, use the return value from any core (should be only one)
            ret_value = next(iter(pending_inst['returns'].values()))
            inst_rsp = INST_RSP_TYPE(
                rsp_time=self.current_cycle,
                id=pending_inst['rv_id'],
                type="nice",
                ret=ret_value,
                xd=(pending_inst['inst'] & 0x4000) > 0
            )
        else:  # vnice
            # For VNICE instructions, aggregate return values from all cores
            # VNICE expects exactly 4 return values in mask bit order
            ret_values = [0, 0, 0, 0]  # Initialize with zeros

            # Get the original instruction to extract mask information
            original_inst = pending_inst['inst']

            # Map core global_id to local core_id within the group
            for core_global_id, ret_val in pending_inst['returns'].items():
                # Find the core by global_id and get its local id within the group
                for i in range(self.col):
                    for j in range(self.row):
                        core = self.npu_cores[i][j]
                        if core.global_id == core_global_id:
                            # Use the core's local id (0-3) as the index
                            # Only include if this core was actually supposed to execute (mask bit set)
                            if 0 <= core.id < 4:
                                ret_values[core.id] = ret_val
                            break

            inst_rsp = INST_RSP_TYPE(
                rsp_time=self.current_cycle,
                id=pending_inst['rv_id'],
                type="vnice",
                ret=ret_values,  # Always exactly 4 elements
                xd=(pending_inst['inst'] & 0x4000) > 0
            )

        self.rsp_queue.put(inst_rsp)
        if self.debug:
            print(f'Generated response for inst_id {inst_id}, type {pending_inst["type"]}, ret {inst_rsp.ret}')
            if pending_inst["type"] == "vnice":
                print(f'VNICE debug: active_cores={pending_inst["cores"]}, returns={pending_inst["returns"]}')
                print(f'VNICE debug: original_inst=0x{pending_inst["inst"]:08x}, aggregated_ret={inst_rsp.ret}')



if __name__ == '__main__':

    # Instantiate a simulation object
    golden_result = False
    Die_test = PNMDie(row=2, col=8, debug=False, gldres=golden_result)

    # Initialize simulation input data
    Die_test.data_import()

    # Run simulation
    # Die_test.run_simulation()
    set_time_step = 500
    Die_test.execute_until(end_time=set_time_step)
    while ~Die_test.simulation_end:
        next_cycle = Die_test.find_next_cycle()
        print('next_cycle = {}; execute_until = {}'.format(next_cycle, next_cycle + set_time_step))
        Die_test.execute_until(end_time= next_cycle + set_time_step)
        if next_cycle == 1768223:
            break


    # Export simulation output result
    Die_test.data_export()

    # Visualization
    visualize = False
    if visualize:
        start_draw_time = time.time()
        for group in range(GlbSet.GROUP_Num_per_Die):
            for id in range(GlbSet.NPU_Num_per_Group):
                target_core = Die_test.find_core_group_id(group, id)
                Vis.visualize(total_cycles=Die_test.total_sim_cycles,
                              visual_title='NPU Core Group-ID @ ' + str(group) + '-' + str(id),
                              ldst_cycles=target_core.prim_record['LDST'],
                              tp_cycles=target_core.prim_record['TP'],
                              vp_cycles=target_core.prim_record['VP'],
                              tm_cycles=target_core.prim_record['TM'],
                              noc_cycles=target_core.prim_record['NOC'],
                              save=True,
                              figsize=(12, 2))
        end_draw_time = time.time()
        draw_time = end_draw_time - start_draw_time
        print(f"Visualization Time Cost: {draw_time:.2f} sec")
