# * * * * * * * * * * * * * * * * #
# On-Chip-Network Model
# Author: <PERSON><PERSON><PERSON>
# * * * * * * * * * * * * * * * * #

import GlobalSettings as GLBSet

# ---------- 2D Mesh ----------


class Node2DMesh:
    def __init__(self, x, y):
        self.x = x
        self.y = y
        self.master_port = False
        self.slave_port = False

    def __repr__(self):
        return f'Node({self.x}, {self.y})'


class Link2DMeshX:
    def __init__(self, x, y):
        self.x = x
        self.y = y
        self.channel_num = GLBSet.NoC_Num_Channel
        self.channel_used = 0

    def not_available(self):
        if self.channel_used == self.channel_num:
            return True
        return False

    def __repr__(self):
        return f'Connect Node({self.x}, {self.y}) and Node({self.x+1}, {self.y})'


class Link2DMeshY:
    def __init__(self, x, y):
        self.x = x
        self.y = y
        self.channel_num = GLBSet.NoC_Num_Channel
        self.channel_used = 0

    def not_available(self):
        if self.channel_used == self.channel_num:
            return True
        return False

    def __repr__(self):
        return f'Connect Node({self.x}, {self.y}) and Node({self.x}, {self.y+1})'


class Mesh2D:
    def __init__(self, row, col):               # 2D Mesh with shape: row × col
        self.row = row
        self.col = col
        self.bit_width = GLBSet.NoC_Data_Bitwidth
        self.routing_algo = 'XY'                # only support static routing algorithms, either XY or YX
        self.nodes = [[Node2DMesh(i, j) for j in range(row)] for i in range(col)]
        self.links_x = [[Link2DMeshX(i, j) for j in range(row)] for i in range(col - 1)]    # links in X direction
        self.links_y = [[Link2DMeshY(i, j) for j in range(row - 1)] for i in range(col)]    # links in Y direction
        self.path_x = []
        self.path_y = []

    def routing_request(self, master, slave):
        # master: [x, y], slave: [x, y]
        # routing algorithm is either XY or YX
        # check if the corresponding links are all available
        # marks all the paths to be traversed when all are available

        master_x, master_y = master
        slave_x, slave_y = slave

        if self.routing_algo == 'XY':
            # XY routing: first route in X-direction, then in Y-direction
            self.path_x, self.path_y = [], []
            current_x, current_y = master_x, master_y

            # Route in X-direction
            while current_x != slave_x:
                next_x = current_x + 1 if slave_x > current_x else current_x - 1
                # Check if the link in X-direction is available
                if self.links_x[current_x][current_y].not_available():
                    return False  # Path not available
                self.path_x.append((current_x, current_y))
                current_x = next_x

            # Route in Y-direction
            while current_y != slave_y:
                next_y = current_y + 1 if slave_y > current_y else current_y - 1
                # Check if the link in Y-direction is available
                if self.links_y[current_x][current_y].not_available():
                    return False  # Path not available
                self.path_y.append((current_x, current_y))
                current_y = next_y

            return True

        elif self.routing_algo == 'YX':
            # YX routing: first route in Y-direction, then in X-direction
            self.path_x, self.path_y = [], []
            current_x, current_y = master_x, master_y

            # Route in Y-direction
            while current_y != slave_y:
                next_y = current_y + 1 if slave_y > current_y else current_y - 1
                # Check if the link in Y-direction is available
                if self.links_y[current_x][current_y].not_available():
                    return False  # Path not available
                self.path_y.append((current_x, current_y))
                current_y = next_y

            # Route in X-direction
            while current_x != slave_x:
                next_x = current_x + 1 if slave_x > current_x else current_x - 1
                # Check if the link in X-direction is available
                if self.links_x[current_x][current_y].not_available():
                    return False  # Path not available
                self.path_x.append((current_x, current_y))
                current_x = next_x

            return True

        else:
            raise ValueError("Unsupported routing algorithm. Only 'XY' or 'YX' are supported.")

    def routing(self, master, slave):
        # The routing_request and routing functions need to be used together.
        # If routing_request returns True, then routing can be performed, which
        # marks the corresponding paths as BUSY.

        for path_x in self.path_x:
            self.links_x[path_x[0]][path_x[1]].channel_used += 1

        for path_y in self.path_y:
            self.links_y[path_y[0]][path_y[1]].channel_used += 1

        self.nodes[master[0]][master[1]].master_port = True
        self.nodes[slave[0]][slave[1]].slave_port = True

        return (path_x, path_y)

    def routing_free(self, master, slave):
        # find corresponding links according to routing algorithm
        # after data transfer, free occupied resources —— links/channels and nodes

        master_x, master_y = master
        slave_x, slave_y = slave

        # Free nodes
        self.nodes[master[0]][master[1]].master_port = False
        self.nodes[slave[0]][slave[1]].slave_port = False

        if self.routing_algo == 'XY':
            # XY routing: first route in X-direction, then in Y-direction
            self.path_x, self.path_y = [], []
            current_x, current_y = master_x, master_y

            # Route in X-direction
            while current_x != slave_x:
                next_x = current_x + 1 if slave_x > current_x else current_x - 1
                # Free one channel in X-direction
                self.links_x[current_x][current_y].channel_used -= 1
                current_x = next_x

            # Route in Y-direction
            while current_y != slave_y:
                next_y = current_y + 1 if slave_y > current_y else current_y - 1
                # Free one channel in Y-direction
                self.links_y[current_x][current_y].channel_used -= 1
                current_y = next_y

            return True

        elif self.routing_algo == 'YX':
            # YX routing: first route in Y-direction, then in X-direction
            self.path_x, self.path_y = [], []
            current_x, current_y = master_x, master_y

            # Route in Y-direction
            while current_y != slave_y:
                next_y = current_y + 1 if slave_y > current_y else current_y - 1
                # Free one channel in Y-direction
                self.links_y[current_x][current_y].channel_used -= 1
                current_y = next_y

            # Route in X-direction
            while current_x != slave_x:
                next_x = current_x + 1 if slave_x > current_x else current_x - 1
                # Free one channel in X-direction
                self.links_x[current_x][current_y].channel_used -= 1
                current_x = next_x

            return True

        else:
            raise ValueError("Unsupported routing algorithm. Only 'XY' or 'YX' are supported.")


if __name__ == '__main__':
    # # Data Structure
    # a = Mesh2D(row=2, col=3)
    # print(a.nodes)
    # print(a.links_x)
    # print(a.links_y)
    # # Routing Example
    # print(a.routing_request(master=[0, 0], slave=[2, 1]))
    # print(a.path_x, a.path_y)
    # a.routing_algo = 'YX'
    # print(a.routing_request(master=[0, 0], slave=[2, 1]))
    # print(a.path_x, a.path_y)
    # a.routing(master=[0, 0], slave=[2, 1])
    # print('links_y.0.0 channel used', a.links_y[0][0].channel_used)
    # a.routing_free(master=[0, 0], slave=[2, 1])
    # print('links_y.0.0 channel used', a.links_y[0][0].channel_used)

    a = Mesh2D(row=2, col=8)
    print(a.nodes)
    print(a.links_x)
    print(a.links_y)


